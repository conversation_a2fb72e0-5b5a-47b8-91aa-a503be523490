<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Cache-Control", "max-age=3600, must-revalidate");
    response.setHeader("Pragma", "cache");
    response.setDateHeader("Expires", System.currentTimeMillis() + 3600000); // 1 hour
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
%>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="/sicsdataanalytics/images/favicon.ico"/>
    <title>SICS Data Analytics</title>

    <!-- Global stylesheets -->
    <link href="/sicsdataanalytics/css/custom.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/amchart.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/css/animate.min.css" id="stylesheet" rel="stylesheet" type="text/css">
    <!--<link href="https://cdn.datatables.net/2.2.1/css/dataTables.dataTables.min.css" id="stylesheet" rel="stylesheet" type="text/css">-->
    <!-- /global stylesheets -->

    <!-- Mandano in 404: bootstrap.bundle.min.js, moment.min.js, noty.min.js -->
    <!-- Risolto: c'era una riga //# sourceMappingURL=html-to-image.js.map che indicava al codice di cercare il file html-to-image.js.map -->

    <!-- Core JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- /core js files -->

    <!-- Theme JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/forms/validation/validate.min.js"></script>
    <!--<script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/forms/validation/localization/messages_it.min.js"></script>-->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/html/layout_1/full/assets/js/app.js"></script>

    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/forms/selects/select2.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/forms/selects/bootstrap_multiselect.js"></script>
    <!--
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/visualization/d3/d3.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/visualization/d3/d3_tooltip.js"></script>
    -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/visualization/echarts/echarts.min.js"></script>

    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/ui/moment/moment.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/pickers/daterangepicker.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/pickers/datepicker.min.js"></script>

    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/forms/wizards/steps.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/demo/pages/components_offcanvas.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/demo/pages/components_popovers.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/bootbox.min.js"></script>

    <script src="/sicsdataanalytics/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
    <!-- /theme js files -->

    <!-- BlockUI JS files -->
    <!-- <script src="/sicsdataanalytics/js/blockUI.js" type="text/javascript"></script> -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" type="text/javascript"></script>
    <!-- /blockui js files -->

    <!-- Datatable JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/tables/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/tables/datatables/extensions/responsive.min.js" type="text/javascript"></script>
    <script src="/sicsdataanalytics/js/datatable-date-euro.js"></script>
    <!-- /datatable js files -->

    <!-- Grid JS files -->
    <!--<script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/tables/gridjs/gridjs.min.js" type="text/javascript"></script>-->
    <!-- /grid js files -->

    <!-- Noty JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/noty.min.js"></script>
    <!-- /noty js files -->

    <!-- Sweet Alert Js files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/sweet_alert.min.js"></script>
    <!-- /sweet alert js files -->

    <!-- JQuery Timer JS files -->
    <script type="text/javascript" src="/sicsdataanalytics/js/jquery.timer.js"></script>
    <!-- /jquery timer js files -->

    <!-- Datepicker JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/demo/pages/picker_date.js"></script>
    <!-- /datepicker js files -->

    <!-- Noui Slider JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/sliders/nouislider.min.js"></script>
    <!-- /noui js files -->

    <!-- Dropzone files -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>
    <!-- /dropzone files -->

    <!-- AmCharts 5 JS files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>
    <script src="/sicsdataanalytics/js/amcharts.js?<%=System.currentTimeMillis()%>"></script>
    <script src="/sicsdataanalytics/js/filters.js?<%=System.currentTimeMillis()%>"></script>

    <script src="/sicsdataanalytics/js/amcharts5/index.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/xy.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/radar.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/percent.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/themes/Animated.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/themes/Responsive.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/locales/en.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/plugins/exporting.js"></script>
    <!-- /amcharts 5 JS files -->

    <!-- HtmlToImage JS files -->
    <script src="/sicsdataanalytics/js/html-to-image.js"></script>
    <!-- /htmltoimage js files -->

    <script type="text/javascript">
        var sweetAlert, notyQueue = new Map();
        var teamsMap = new Map(), oppositeEventType = new Map(), oppositeTagType = new Map(), playersMap = new Map(), advancedMetricMap = new Map();
        var globalMessages = new Map();
        var personalFilters = new Map();
        var filtersIndex = ["totaltype", "seasonid", "competitionid", "groupid", "matchday", "teamid", "playerid", "eventtypeid", "eventtypeids", "advancedeventtypeid", "tacticaleventtypeid", "tagtypeid", "tagtypeids", "ishometeam", "homemodule", "awaymodule", "fixtureid", "half", "zone", "channel", "area", "distance", "eventtypeid-x", "eventtypeid-y", "countryid", "bornyear", "positionid", "positiondetailid", "footid", "eventamount", "anglezone", "playtime", "tagtypeid-x", "tagtypeid-y", "eventamount-y", "statstypeid", "importance", "hidebest"];
        let baseUrl = "${mBaseUrl}", socketUserId = "${mUser.id}", userLanguage = "${mUser.tvLanguage}";
        var lastEventTabClicked = "";
        var minPlaytimeFilter;
        var preferredTeamId = null;
        var loadGroupIdsFromCache = true;

        var loadAllEventTypes = false;

        function checkSession() {
            $.ajax({
                type: "GET",
                url: "/sicsdataanalytics/user/isValidSession.htm",
                cache: false,
                success: function (msg) {
                    if (msg === "askClearSession") {
                        window.location.href = "/sicsdataanalytics/auth/multipleSession.htm";
                    } else if (msg === "expired") {
                        window.location.href = "/sicsdataanalytics/auth/login.htm?expired=true";
                    } else if (msg !== "true") {
                        window.location.href = "/sicsdataanalytics/auth/login.htm?session=disposed";
                    }

                    setTimeout(function () {
                        checkSession();
                    }, 5000);
                }
            });
        }

        $(document).ready(function () {
            if (!document.URL.includes("/auth/")) {
                checkSession();
                initializeSocket();
            }

            // Default initialization
            $('.select').select2({
                dropdownAutoWidth: true
            });
            $(document).on('select2:open', function () {
                document.querySelector('.select2-search__field').focus();
                $('input.select2-search__field').prop('placeholder', '<spring:message code="filters.search"/>');
            });

            $('.multiselect:not(.no-search)').multiselect({
                selectAllText: '<spring:message code="filters.select.all"/>',
                enableFiltering: true,
                includeFilterClearBtn: false,
                enableCaseInsensitiveFiltering: true,
                enableClickableOptGroups: true,
                enableCollapsibleOptGroups: true,
                collapseOptGroupsByDefault: true,
                onDropdownShown: function (event) {
                    var $dropdown = $(event.target).siblings('.dropdown-menu');
                    $('body').append($dropdown.detach());

                    // Reposition it under the button:
                    var buttonOffset = $(event.target).offset();
                    $dropdown.css({
                        'position': 'absolute',
                        'top': buttonOffset.top + $(event.target).outerHeight(),
                        'left': buttonOffset.left,
                        'width': 'auto',
                        'z-index': 9999,
                        'display': 'block',
                        'max-height': '40vh'
                    });

                    if (typeof this.$filter !== "undefined") {
                        $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                        this.$filter.find('.multiselect-search').focus();
                        this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                    }
                },
                onDropdownHidden: function (event) {
                    var $dropdown = $('body > .dropdown-menu');
                    $(event.target).after($dropdown.detach());
                    $dropdown.hide();
                }
            });
            // fix css per search
            $('.multiselect-search').css('padding-left', 'calc(0.875rem * 2 + 1.25rem)');
            // Prevent deselect
            var $preventDeselectElement = $('.multiselect-prevent-deselect');
            $preventDeselectElement.multiselect({
                onChange: function (option, checked) {
                    if (checked === false) {
                        $preventDeselectElement.multiselect('select', option.val());
                    }
                }
            });

            // Override Noty defaults
            Noty.overrideDefaults({
                theme: 'limitless',
                layout: 'topRight',
                type: 'alert',
                callbacks: {
                    onShow: function () {
                        if (this && this.options && this.options.text) {
                            if (notyQueue.has(this.options.text)) {
                                this.options.skipCheck = true;
                                this.close();
                                this.stop();
                            } else {
                                notyQueue.set(this.options.text, this.options);
                            }
                        }
                    }, onClose: function () {
                        if (this && this.options && this.options.text) {
                            if (typeof this.options.skipCheck === "undefined") {
                                notyQueue.delete(this.options.text);
                            }
                        }
                    }
                },
                timeout: 2500
            });

            // Defaults
            sweetAlert = swal.mixin({
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-light',
                    denyButton: 'btn btn-light',
                    input: 'form-control'
                }
            });

            // Default initialization datepicker
            // Single picker
            $('.daterange-single').daterangepicker({
                parentEl: '.content-inner',
                singleDatePicker: true
            });

            const customPopoverElement = document.querySelector('[data-bs-popup=popover]');
            if (customPopoverElement) {
                new bootstrap.Popover(customPopoverElement);
            }

            // event tab listener
            $("#event-container-nav").find("a[data-bs-toggle='tab']").on("shown.bs.tab", function () {
                let clicked = $(this).attr("href");
                setTimeout(function () {
                    if (lastEventTabClicked !== clicked) {
                        lastEventTabClicked = clicked;
                        if (lastEventTabClicked.includes("-advanced")) {
                            if ($("#filter-advancedeventtypeid").val()) {
                                if (typeof updateChart === "function") {
                                    updateChart();
                                }
                            }
                        } else if (lastEventTabClicked.includes("-tactical")) {
                            if ($("#filter-tacticaleventtypeid").val()) {
                                if (typeof updateChart === "function") {
                                    updateChart();
                                }
                            }
                        } else {
                            if ($("#filter-eventtypeid").val()) {
                                if (typeof updateChart === "function") {
                                    updateChart();
                                }
                            }
                        }
                    }
                }, 250);
            });

            // bind search input
            $("#navbar-search-input").on("input", function () {
                $("#navbar-search-text").text("\"" + $(this).val() + "\"");
                if (!$("#navbar-search-dropdown").hasClass("show")) {
                    $("#navbar-search-input").click();
                }
            });
            $("#navbar-search-input").on('keyup', function (e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    search();
                }
            });

            // bind event builder modal
            // (!) SE TOCCHI QUALCOSA C'E' DA PORTARLO ANCHE SULLA PAGINA SCATTERPLOT (!)
            // (!) SE TOCCHI QUALCOSA C'E' DA PORTARLO ANCHE SULLA PAGINA SCATTERPLOT (!)
            // (!) SE TOCCHI QUALCOSA C'E' DA PORTARLO ANCHE SULLA PAGINA SCATTERPLOT (!)
            $("#modal-event-builder").on('show.bs.modal', function (event) {
                if (typeof eventFilter.scatterplot === "undefined") {
                    if (typeof eventFilter.allEvents !== "undefined") {
                        loadEventFilterModal();
                    } else {
                        // c'� qualcosa che non va
                        event.preventDefault();
                        event.stopPropagation();

                        new Noty({
                            text: globalMessages.get("error.unexpected"),
                            type: "error",
                            layout: "topCenter"
                        }).show();
                    }
                }
            });

            // personal filters map
        <c:if test="${mFilters != null}">
            <c:forEach var="filter" items="${mFilters}">
            personalFilters.set(${filter.id}, eval(${filter.getJson()}));
            </c:forEach>
        </c:if>

            // messages map
        <c:if test="${mGlobalMessages != null}">
            <c:forEach var="key" items="${mGlobalMessages.keySet()}">
            globalMessages.set("${key}", "${mGlobalMessages.get(key)}");
            </c:forEach>
        </c:if>

            // teams map
        <c:if test="${mGlobalTeams != null}">
            <c:forEach var="team" items="${mGlobalTeams.values()}">
            teamsMap.set(${team.id}, eval(${team.getJson()}));
            </c:forEach>
        </c:if>
        <c:if test="${mPreferredTeam != null}">
            teamsMap.set(${mPreferredTeam.id}, eval(${mPreferredTeam.getJson()}));
            setPreferredTeam(${mPreferredTeam.id});
        </c:if>
            // opposite event type map
        <c:if test="${mOppositeEventType != null}">
            <c:forEach var="tagType" items="${mOppositeEventType.values()}">
            oppositeEventType.set(${tagType.id}, eval(${tagType.getJson()}));
            </c:forEach>
        </c:if>
            // opposite tag type map
        <c:if test="${mOppositeTagType != null}">
            <c:forEach var="eventType" items="${mOppositeTagType.values()}">
            oppositeTagType.set(${eventType.id}, eval(${eventType.getJson()}));
            </c:forEach>
        </c:if>
            // players map
        <c:if test="${mPlayers != null}">
            <c:forEach var="player" items="${mPlayers.values()}">
            playersMap.set(${player.id}, eval(${player.getJson()}));
            </c:forEach>
        </c:if>
            // advanced metrics map
        <c:if test="${mAdvancedMetrics != null}">
            <c:forEach var="metric" items="${mAdvancedMetrics.values()}">
            advancedMetricMap.set(${metric.id}, eval(${metric.getJson()}));
            </c:forEach>
        </c:if>

            bindFiltersSave();

            // bind search input for team preferred
            $("#navbar-search-team-preferred-input").on("input", function () {
                $("#navbar-search-team-preferred-text").text("\"" + $(this).val() + "\"");
                if (!$("#navbar-search-team-preferred-dropdown").hasClass("show")) {
                    $("#navbar-search-team-preferred-input").click();
                }
            });
            $("#navbar-search-team-preferred-input").on('keyup', function (e) {
                if (e.key === 'Enter' || e.keyCode === 13) {
                    searchTeamPreferred();
                }
            });

            $(".update-groupid").on("change", function () {
                let seasonId = $("#filter-seasonid").val();
                let competitionId = $(this).val();
                if (seasonId && competitionId) {
                    let filterId = "groupid";
                    var previousValue = $("#filter-" + filterId).val();

                    $("#filter-" + filterId + " option").remove();
                    $("#filter-" + filterId + " optgroup").remove();
                    if ($("#filter-" + filterId).attr("multiple") !== "multiple") {
                        $("#filter-" + filterId).append($("<option>"));
                    }
                    $("#filter-" + filterId + "-container").addClass("d-none");

                    if ($.isArray(competitionId) && competitionId.length > 1) {
                        new Noty({
                            text: "<spring:message code="messages.group.single.competition.warning"/>",
                            type: "warning",
                            layout: "topCenter"
                        }).show();
                        return;
                    }
                    $.ajax({
                        type: "GET",
                        url: "/sicsdataanalytics/user/getCompetitionGroups.htm",
                        cache: false,
                        data: encodeURI("seasonId=" + seasonId + "&competitionId=" + competitionId),
                        success: function (result) {
                            if (notEmpty(result)) {
                                var object = JSON.parse(result);

                                Object.keys(object).forEach(function (index) {
                                    var element = object[index];
                                    let option = $("<option>", {
                                        value: element.id,
                                        text: getDescriptionInLanguage(element, "${mUser.tvLanguage}")
                                    });

                                    $("#filter-" + filterId).append(option);
                                });

                                if ($("#filter-" + filterId).attr("multiple") === "multiple") {
                                    $("#filter-" + filterId).multiselect('rebuild');
                                }

                                if ($("#filter-" + filterId).hasClass("load-all-on-change") && $("#filter-" + filterId).attr("multiple") === "multiple") {
                                    // se la select ha la classe "load-all-on-change" imposto tutti i valori
                                    $("#filter-" + filterId).find("option").each(function () {
                                        $("#filter-" + filterId).multiselect('select', $(this).val());
                                    });
                                    $("#filter-" + filterId).trigger("change");
                                } else {
                                    if (keepFilterValue) {
                                        // console.log(filterId, previousValue, notEmpty(previousValue));
                                        if (notEmpty(previousValue)) {
                                            // se la select ha quel valore allora lo imposto
                                            if ($("#filter-" + filterId).attr("multiple") === "multiple") {
                                                previousValue.forEach(function (element) {
                                                    $("#filter-" + filterId).multiselect('select', element);
                                                });
                                                // $("#filter-" + filterId).trigger("change");
                                            } else if ($("#filter-" + filterId + " option[value='" + previousValue + "']").length > 0) {
                                                if ($.isArray(previousValue)) {
                                                    $("#filter-" + filterId).val(previousValue);
                                                } else {
                                                    $("#filter-" + filterId).val(previousValue);
                                                }
                                            }
                                        }
                                    }
                                }

                                $("#filter-" + filterId + "-container").removeClass("d-none");
                                animate("filter-" + filterId);

                                if (loadGroupIdsFromCache) {
                                    loadGroupIdsFromCache = false;
                                    let index = filtersIndex.indexOf("groupid");
                                    if (index >= 0) {
                                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                        if (index > 25) {
                                            index -= 25;
                                            letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                        }
                                        let value = localStorage.getItem("dataanalytics/610/" + letter + "/filter-groupid");
                                        if (notEmpty(value)) {
                                            value.split(",").forEach(function (option) {
                                                $("#filter-groupid").multiselect('select', option).change();
                                            });
                                        }
                                    }
                                }
                            }
                        },
                        error: function () {
                            sweetAlert.fire({
                                title: "ERROR, Oops...",
                                text: "<spring:message code="messages.generic.error"/>",
                                icon: "error",
                                padding: 40
                            });
                        }
                    });
                }
            });
        });

        function bindFiltersSave() {
            // registrazione salvataggio filtri
            $(".form-control.is-filter").on("change custom-change", function () {
                let elementId = $(this).attr("id");
                if (elementId.startsWith("filter-")) {
                    let value = $(this).val();
                    if ($(this).attr("type") === "checkbox") {
                        value = $(this).is(":checked") ? "true" : "";
                    }
                    let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                    if (index >= 0) {
                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        if (index > 25) {
                            index -= 25;
                            letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                        }
                        if (notEmpty(value)) {
                            localStorage.setItem("dataanalytics/610/" + letter + "/" + elementId, value);
                        } else {
                            localStorage.removeItem("dataanalytics/610/" + letter + "/" + elementId);
                        }
//                        console.log("update", $(this).attr("id"));
                    } else {
                        // potrebbe essere filtro extra
                        filtersIndex.forEach(function (element) {
                            if (elementId.startsWith("filter-" + element) && elementId !== ("filter-" + element)) {
                                let index = filtersIndex.indexOf(element);
                                if (index >= 0) {
                                    let additionalIndex = elementId.replace("filter-" + element + "-", "");
                                    if (notEmpty(additionalIndex)) {
                                        let page = location.pathname.split("/").pop().replace(".htm", "");
                                        if (notEmpty(page)) {
                                            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                            if (index > 25) {
                                                index -= 25;
                                                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                            }
                                            if (notEmpty(value)) {
                                                localStorage.setItem("dataanalytics/610/Z/" + letter + "/" + page + "/" + additionalIndex + "/" + elementId, value);
                                            } else {
                                                localStorage.removeItem("dataanalytics/610/Z/" + letter + "/" + page + "/" + additionalIndex + "/" + elementId);
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }

                    if (needToShowFilterMessage()) {
                        $("#specific-filter-message").removeClass("d-none");
                    } else {
                        $("#specific-filter-message").addClass("d-none");
                    }
                }

                updateFiltersColor();
            });
        }

        // Set up global error handling
        window.onerror = function (message, source, lineno, colno, error) {
            new Noty({
                text: "<spring:message code="messages.generic.error"/>",
                type: "error",
                layout: "topCenter"
            }).show();

            const lastSent = localStorage.getItem('dataanalytics/610/lastErrorDate');
            const now = Date.now();
            if (lastSent === null || (now - lastSent > 60000)) {
                localStorage.setItem('dataanalytics/610/lastErrorDate', now);
                sendMessage("0x00A" + error.stack);
            } else {
                console.warn("Error not sended to server (last error sent in the last minute)");
            }
        };

        window.addEventListener('beforeunload', function (event) {
            // questo evento viene richiamato ogni volta prima che si esce dalla pagina
            // quindi sia per navigazione avanti e indietro, che per reload della pagina
            showBlockUI();
        });

        function showBlockUI() {
            $.blockUI({
                message: '<i class="icon-spinner4 spinner"></i>',
                overlayCSS: {
                    backgroundColor: '#1b2024',
                    opacity: 0.8,
                    cursor: 'wait'
                },
                css: {
                    border: 0,
                    color: '#fff',
                    padding: 0,
                    backgroundColor: 'transparent'
                }
            });
        }

        function closeBlockUI() {
            $.unblockUI();
        }

        function removeParamFromURL(url, paramName, paramValue) {
            url = url.replace("?" + paramName + "=" + paramValue, "");
            url = url.replace("&" + paramName + "=" + paramValue, "");
            return url;
        }

        function replaceParamFromURL(url, paramName, oldValue, newValue) {
            return url = url.replace(paramName + "=" + oldValue, paramName + "=" + newValue);
        }

        function addParamToURL(url, paramName, paramValue) {
            if (url.includes("?")) {
                url += "&" + paramName + "=" + paramValue;
            } else {
                url += "?" + paramName + "=" + paramValue;
            }
            return url;
        }

        function blockElement(element) {
            $(element).block({
                message: '<i class="icon-spinner4 spinner"></i>',
                overlayCSS: {
                    backgroundColor: '#ffffff',
                    opacity: 0.8,
                    cursor: 'wait'
                },
                css: {
                    width: 'auto',
                    border: 0,
                    padding: 0
                }
            });

            // dopo 5 secondi massimo rilascio (probabilmente � andato in errore)
            let currentTime = Date.now();
            $(element).data().blockTime = currentTime;
            setTimeout(function () {
                if ($(element).data().blockTime === currentTime) {
                    $(element).unblock();
                }
            }, 5000);
        }

        function reloadTooltips() {
            // https://getbootstrap.com/docs/5.0/components/tooltips/
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-popup="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        function changePassword() {
            // Clear previous values and errors
            $('#new-password').val('');
            $('#confirm-password').val('');
            $('#password-error').addClass('d-none');

            // Show the password reset modal
            $('#modal-password-change').modal('show');
        }

        function submitPasswordChange() {
            var newPassword = $('#new-password').val();
            var confirmPassword = $('#confirm-password').val();

            // Validate passwords match
            if (newPassword !== confirmPassword) {
                $('#password-error').removeClass('d-none');
                return;
            }

            // Hide error message if previously shown
            $('#password-error').addClass('d-none');

            // Submit the new password
            $.ajax({
                type: "POST",
                url: "/sicsdataanalytics/user/updatePassword.htm",
                data: encodeURI("password=" + newPassword),
                cache: false,
                success: function (result) {
                    // Hide the modal
                    $('#modal-password-change').modal('hide');

                    if (result === "ok") {
                        sweetAlert.fire({
                            title: "<spring:message code="messages.change.password"/>",
                            text: "<spring:message code="messages.change.password.confirm"/>",
                            icon: "success",
                            padding: 40
                        });
                    } else {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                },
                error: function () {
                    // Hide the modal
                    $('#modal-password-change').modal('hide');

                    sweetAlert.fire({
                        title: "ERROR, Oops...",
                        text: "<spring:message code="messages.generic.error"/>",
                        icon: "error",
                        padding: 40
                    });
                }
            });
        }

        function search() {
            $("#navbar-search-result").empty();
            let input = $("#navbar-search-input").val();
            if (input) {
                event.preventDefault();
                event.stopPropagation();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/user/search.htm",
                    cache: false,
                    data: encodeURI("input=" + input),
                    success: function (result) {
                        $("#navbar-search-result").html(result);
                        highlightSearchInput();
                    },
                    error: function () {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            }
        }

        function searchProfile(id, isTeam) {
            if (typeof id !== "undefined" && typeof isTeam !== "undefined") {
                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/user/getLastData.htm",
                    cache: false,
                    data: encodeURI("id=" + id + "&isTeam=" + isTeam),
                    success: function (result) {
                        if (notEmpty(result)) {
                            var object = JSON.parse(result);
                            if (Object.keys(object).length > 0) {
                                Object.keys(object).forEach(function (filterId) {
                                    let index = filtersIndex.indexOf(filterId);
                                    if (index >= 0) {
                                        let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                        if (index > 25) {
                                            index -= 25;
                                            letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                        }
                                        localStorage.setItem("dataanalytics/610/" + letter + "/filter-" + filterId, object[filterId]);
                                    }
                                });

                                if (isTeam === true) {
                                    location.href = "/sicsdataanalytics/team/overview.htm";
                                } else {
                                    location.href = "/sicsdataanalytics/player/overview.htm";
                                }
                            } else {
                                sweetAlert.fire({
                                    title: "ERROR, Oops...",
                                    text: "<spring:message code="messages.generic.error"/>",
                                    icon: "error",
                                    padding: 40
                                });
                            }
                        } else {
                            sweetAlert.fire({
                                title: "ERROR, Oops...",
                                text: "<spring:message code="messages.generic.error"/>",
                                icon: "error",
                                padding: 40
                            });
                        }
                    },
                    error: function () {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            }
        }

        function searchTeamPreferred() {
            $("#navbar-search-team-preferred-result").empty();
            let input = $("#navbar-search-team-preferred-input").val();
            if (input) {
                event.preventDefault();
                event.stopPropagation();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/user/search.htm",
                    cache: false,
                    data: encodeURI("input=" + input + "&loadPlayers=false"),
                    success: function (result) {
                        $("#navbar-search-team-preferred-result").html(result);
                        $("#navbar-search-team-preferred-result").find("div.dropdown-item.cursor-pointer").each(function (index, element) {
                            // let's change the onclick action
                            $(element).attr("onclick", "setPreferredTeam(" + $(element).attr("itemid") + ")");
                        });
                        highlightSearchTeamPreferredInput();
                    },
                    error: function () {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            }
        }

        function showAgencyDetails(id) {
            if (id) {
                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/user/getAgencyDetails.htm",
                    cache: false,
                    data: encodeURI("id=" + id),
                    success: function (result) {
                        if (notEmpty(result)) {
                            $("#offcanvas-agency-details").html(result);
                            new bootstrap.Offcanvas($("#offcanvas-agency-details")).show();
                        } else {
                            sweetAlert.fire({
                                title: "ERROR, Oops...",
                                text: "<spring:message code="messages.generic.error"/>",
                                icon: "error",
                                padding: 40
                            });
                        }
                    },
                    error: function () {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "<spring:message code="messages.generic.error"/>",
                    icon: "error",
                    padding: 40
                });
            }
        }

        function setPreferredTeam(id) {
            if (teamsMap.has(id)) {
                let team = teamsMap.get(id);
                if (team) {
                    preferredTeamId = team.id;
                    $("#modal-preferred-team-current").removeClass("d-none");
                    $("#modal-preferred-team-current-img").attr("src", "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + team.logo + ".png");
                    $("#modal-preferred-team-current-name").text(getDescriptionInLanguage(team, "${mUser.tvLanguage}"));
                } else {
                    sweetAlert.fire({
                        title: "ERROR, Oops...",
                        text: "<spring:message code="messages.generic.error"/>",
                        icon: "error",
                        padding: 40
                    });
                }
            } else {
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "<spring:message code="messages.generic.error"/>",
                    icon: "error",
                    padding: 40
                });
            }
        }

        function savePreferredTeam() {
            if (preferredTeamId) {
                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/user/savePreferredTeam.htm",
                    cache: false,
                    data: encodeURI("id=" + preferredTeamId),
                    success: function (result) {
                        if (result === "ok") {
                            sweetAlert.fire({
                                title: "<spring:message code="action.confirmation"/>",
                                text: "<spring:message code="preferred.team.save.success.title"/>",
                                icon: "success",
                                padding: 40
                            }).then(function () {
                                location.reload();
                            });
                        } else {
                            sweetAlert.fire({
                                title: "ERROR, Oops...",
                                text: "<spring:message code="messages.generic.error"/>",
                                icon: "error",
                                padding: 40
                            });
                        }
                    },
                    error: function () {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "<spring:message code="messages.generic.error"/>",
                    icon: "error",
                    padding: 40
                });
            }
        }

        function removePreferredTeam() {
            $.ajax({
                type: "GET",
                url: "/sicsdataanalytics/user/savePreferredTeam.htm",
                cache: false,
                data: encodeURI("id="),
                success: function (result) {
                    if (result === "ok") {
                        sweetAlert.fire({
                            title: "<spring:message code="action.confirmation"/>",
                            text: "<spring:message code="preferred.team.remove.success.title"/>",
                            icon: "success",
                            padding: 40
                        }).then(function () {
                            location.reload();
                        });
                    } else {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                },
                error: function () {
                    sweetAlert.fire({
                        title: "ERROR, Oops...",
                        text: "<spring:message code="messages.generic.error"/>",
                        icon: "error",
                        padding: 40
                    });
                }
            });
        }
    </script>

    <!-- Questo sta sotto perch� mi serve la variabile baseUrl -->
    <!-- WebSocket JS files -->
    <script src="/sicsdataanalytics/js/websocket.js" type="text/javascript"></script>
    <!-- /websocket js files -->
</head>

<!-- Header Body content -->

<!-- Warning modal -->
<div id="modalAskSession" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white border-0">
                <h6 class="modal-title"><spring:message code='auth.login.other.device'/></h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <h6 class="fw-semibold"><spring:message code='menu.multi.session.header'/></h6>
                <p><spring:message code='menu.multi.session.content'/></p>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-link"><spring:message code='menu.multi.session.force'/></button>
                <button type="button" class="btn btn-warning"><spring:message code='menu.multi.session.cancel'/></button>
            </div>
        </div>
    </div>
</div>
<!-- /warning modal -->

<!-- Event Builder modal -->
<div id="modal-event-builder" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-full">
        <div class="modal-content">
            <div class="modal-header bg-info text-white border-0">
                <h6 class="modal-title"><spring:message code="event.filter.modal.title"/></h6>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" onclick="updateChart();"></button>
            </div>

            <div class="modal-body vh-55 overflow-auto">
                <div class="row">

                    <div class="col-3">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="modal-event-container-nav">
                            <li class="nav-item">
                                <a href="#modal-event-filters-event" class="nav-link active" data-bs-toggle="tab" type="1">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#modal-event-filters-advanced" class="nav-link" data-bs-toggle="tab" type="2">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#modal-event-filters-tactical" class="nav-link" data-bs-toggle="tab" type="3">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-0" id="modal-event-tabs">
                            <div class="tab-pane fade show active" id="modal-event-filters-event">
                                <div class="list-group vh-40 overflow-auto" id="event-builder-event-list">

                                </div>
                            </div>

                            <div class="tab-pane fade" id="modal-event-filters-advanced">
                                <div class="list-group vh-40 overflow-auto" id="event-builder-advanced-list">

                                </div>
                            </div>

                            <div class="tab-pane fade" id="modal-event-filters-tactical">
                                <div class="list-group vh-40 overflow-auto" id="event-builder-tactical-list">

                                </div>
                            </div>
                        </div>

                        <a type="button" class="list-group-item list-group-item-action d-flex py-1 px-2 cursor-pointer d-none" id="base-event-item">
                            <span class="event-text"></span>
                            <div class="ms-auto icon-container">
                                <i class="ph-tag" id="event-item-tags" title="<spring:message code="event.filter.modal.has.tags"/>"></i>
                                <i class="ph-sliders" id="event-item-positions" title="<spring:message code="event.filter.modal.has.positions"/>"></i>
                            </div>
                        </a>
                    </div>

                    <div class="col-3">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2">
                            <li class="nav-item">
                                <a href="#modal-event-tags" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="event.filter.modal.tag.picker"/>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content px-0">
                            <div class="tab-pane fade show active" id="modal-event-tags">
                                <div class="list-group vh-40 overflow-auto d-none" id="event-builder-tag-list">

                                </div>
                                <div class="list-group vh-40 overflow-auto d-none" id="empty-event-builder-tag-list">
                                    <label class="list-group-item list-group-item-action py-1 px-2 text-center">
                                        <spring:message code="event.filter.modal.tag.picker.empty"/>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <label class="list-group-item list-group-item-action py-1 px-2 d-none" id="base-tag-item">
                            <input type="checkbox" class="form-check-input me-2">
                            <span class="event-text"></span>
                        </label>
                    </div>

                    <div class="col-3">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2">
                            <li class="nav-item">
                                <a href="#modal-event-position" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="event.filter.modal.position.picker"/>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content px-0">
                            <div class="tab-pane fade show active w-100">
                                <div id="modal-event-position-container">
                                    <div id="modal-event-position">

                                    </div>
                                    <div class="d-flex align-items-center mt-1">
                                        <button type="button" class="btn btn-light px-2 py-1" onclick="changeOptions();">
                                            <i class="ph-number-circle-one" id="modal-change-positional"></i>
                                        </button>
                                        <p class="mb-0 ms-2 text-muted" id="modal-positional-description"><spring:message code="event.filter.modal.positional.half"/></p>
                                    </div>
                                </div>
                                <div class="list-group vh-40 overflow-auto" id="empty-modal-event-position">
                                    <label class="list-group-item list-group-item-action py-1 px-2 text-center">
                                        <spring:message code="event.filter.modal.position.picker.empty"/>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-3">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2">
                            <li class="nav-item">
                                <a href="#modal-current-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="event.filter.modal.current.params"/>
                                </a>
                            </li>
                        </ul>
                        <div class="tab-content px-0">
                            <div class="tab-pane fade show active" id="modal-current-event">
                                <div class="list-group vh-40 overflow-auto">
                                    <div class="table-responsive">
                                        <table class="table text-nowrap" id="modal-current-parameters-table">
                                            <thead>
                                                <tr>
                                                    <th class="w-100"><spring:message code="event.filter.modal.event.tags"/></th>
                                                    <th><spring:message code="event.filter.modal.position"/></th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="modal-current-event-scatterplot">
                                <div class="list-group overflow-auto">
                                    <div class="table-responsive">
                                        <table class="table text-nowrap" id="modal-current-parameters-table-scatterplot-x">
                                            <thead>
                                                <tr>
                                                    <th class="w-100">X <spring:message code="event.filter.modal.event.tags"/></th>
                                                    <th><spring:message code="event.filter.modal.position"/></th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="list-group overflow-auto mt-4">
                                    <div class="table-responsive">
                                        <table class="table text-nowrap" id="modal-current-parameters-table-scatterplot-y">
                                            <thead>
                                                <tr>
                                                    <th class="w-100">Y <spring:message code="event.filter.modal.event.tags"/></th>
                                                    <th><spring:message code="event.filter.modal.position"/></th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-link" data-bs-dismiss="modal" onclick="updateChart();"><spring:message code="event.filter.modal.confirm"/></button>
                <div class="d-none" id="modal-add-parameter-container">
                    <button type="button" class="btn btn-info" onclick="saveCurrentEventFilter();"><spring:message code="event.filter.modal.add.parameter"/></button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- /event builder modal -->

<!-- Preferred Team modal -->
<div id="modal-preferred-team" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <h6 class="lh-base mb-0"><spring:message code="preferred.team"/></h6>
                    <spring:message code="preferred.team.subtitle"/>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body vh-50 overflow-auto">
                <div class="d-flex flex-column h-100">
                    <div>
                        <div class="w-100 d-flex flex-column justify-content-center align-items-center">
                            <h3 class="mb-0"><spring:message code="preferred.team.picker.title"/></h3>
                            <p class="mb-2 text-muted"><spring:message code="preferred.team.picker.subtitle"/></p>
                            <div class="form-control-feedback form-control-feedback-start w-75" data-color-theme="dark">
                                <input type="text" id="navbar-search-team-preferred-input" class="form-control rounded-pill" placeholder="<spring:message code="preferred.team.picker.placeholder"/>" data-bs-toggle="dropdown" autocomplete="off">
                                <div class="form-control-feedback-icon">
                                    <i class="ph-magnifying-glass"></i>
                                </div>
                                <div class="dropdown-menu w-100" data-color-theme="light" id="navbar-search-team-preferred-dropdown">
                                    <button type="button" class="dropdown-item" onclick="searchTeamPreferred();">
                                        <div class="text-center w-32px me-3">
                                            <i class="ph-magnifying-glass"></i>
                                        </div>
                                        <span><spring:message code="preferred.team.picker.placeholder"/> <span class="fw-bold" id="navbar-search-team-preferred-text">""</span></span>
                                    </button>

                                    <div id="navbar-search-team-preferred-result">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="mt-3"/>
                        <div id="modal-preferred-team-current" class="${mUser.preferredTeamId != null ? '' : 'd-none'}">
                            <div class="w-100 d-flex flex-column justify-content-center align-items-center">
                                <h1 class="mb-0"><spring:message code="preferred.team.current.team"/></h1>
                                <div class="row mt-2">
                                    <div class="col">
                                        <img id="modal-preferred-team-current-img" src="" class="w-48px h-48px rounded-pill image-shadow-sm" alt="" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png'">
                                        <span class="h5 fw-normal" id="modal-preferred-team-current-name"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-auto">
                        <i class="ph-info text-primary"></i>
                        <span class="ms-1"><spring:message code="preferred.team.info"/></span>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn" data-bs-dismiss="modal"><spring:message code="preferred.team.close"/></button>
                <c:if test="${mPreferredTeam != null}">
                    <button type="button" class="btn btn-danger" onclick="removePreferredTeam();"><spring:message code="preferred.team.remove"/></button>
                </c:if>
                <button type="button" class="btn btn-primary" onclick="savePreferredTeam();"><spring:message code="preferred.team.save"/></button>
            </div>
        </div>
    </div>
</div>
<!-- /event builder modal -->

<!-- /Agency Details Offcanvas -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvas-agency-details">

</div>
<!-- /agency details offcanvas -->

<!-- Password Change modal -->
<div id="modal-password-change" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <h5 class="mb-0"><spring:message code="messages.change.password"/></h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="password-change-form">
                    <div class="mb-3">
                        <label class="form-label"><spring:message code="auth.login.password"/></label>
                        <div class="input-group">
                            <input type="password" id="new-password" class="form-control" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><spring:message code="auth.login.password.confirm"/></label>
                        <div class="input-group">
                            <input type="password" id="confirm-password" class="form-control" required>
                        </div>
                    </div>
                    <div id="password-error" class="text-danger d-none">
                        <spring:message code="auth.login.password.mismatch"/>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-link" data-bs-dismiss="modal"><spring:message code="action.cancel"/></button>
                <button type="button" class="btn btn-primary" onclick="submitPasswordChange();"><spring:message code="action.save"/></button>
            </div>
        </div>
    </div>
</div>
<!-- /password reset modal -->

<!-- Screenshot Collection Manager modal -->
<div id="modal-screenshot-collection" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered" style="--modal-width: 700px">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">
                    <h5 class="mb-0"><i class="ph-image me-2"></i><spring:message code="pdf.screenshot.modal.title"/></h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="ph-image ph-2x text-primary mb-2"></i>
                                <h6 class="mb-0">Screenshots</h6>
                                <span id="collection-screenshot-count" class="fs-4 fw-bold text-primary">0</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="ph-hard-drives ph-2x text-info mb-2"></i>
                                <h6 class="mb-0"><spring:message code="pdf.screenshot.modal.estimated.size"/></h6>
                                <span id="collection-estimated-size" class="fs-4 fw-bold text-info">0 KB</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-muted mb-0">
                        <i class="ph-info me-1"></i>
                        <spring:message code="pdf.screenshot.modal.informations"/>
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" onclick="addScreenshotToCollection();">
                    <i class="ph-plus me-2"></i><spring:message code="pdf.screenshot.modal.add.screenshot"/>
                </button>
                <button type="button" class="btn btn-primary" id="btn-download-collection" onclick="downloadCollection();" disabled>
                    <i class="ph-download me-2"></i><spring:message code="pdf.screenshot.modal.download.collection"/>
                </button>
                <button type="button" class="btn btn-danger" id="btn-delete-collection" onclick="deleteCollection();" disabled>
                    <i class="ph-trash me-2"></i><spring:message code="pdf.screenshot.modal.delete.collection"/>
                </button>
                <button type="button" class="btn btn-link" data-bs-dismiss="modal"><spring:message code="pdf.screenshot.modal.close"/></button>
            </div>
        </div>
    </div>
</div>
<!-- /Screenshot Collection Manager modal -->

<!-- /header body content -->
