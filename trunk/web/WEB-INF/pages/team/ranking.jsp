<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    var matchdaySlider;
    $(document).ready(function () {
        $("#rankingButton").addClass("active");

        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();
            // non faccio niente perch� c'� evento bindato sul campo input
        });

        showChartMessage(1);

        // Define element
        const slider_drag_behaviour = document.getElementById('noui-slider-drag');
        // Create slider
        matchdaySlider = noUiSlider.create(slider_drag_behaviour, {
            start: [1, 100],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 100
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const slider_drag_behaviour_vals = [
            document.getElementById('noui-slider-drag-lower-val'),
            document.getElementById('noui-slider-drag-upper-val')
        ];
        // Show the values
        slider_drag_behaviour.noUiSlider.on('update', function (values, handle) {
            slider_drag_behaviour_vals[handle].innerHTML = values[handle];
        });

        matchdaySlider.on("change", function () {
            let index = filtersIndex.indexOf("matchday");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-matchday", matchdaySlider.get());
            }
            updateChart("matchday");
        });

        checkInitialFilters();
    });

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();

        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");

//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let seasonId = $("#filter-seasonid").val();
            let competitionId = $("#filter-competitionid").val();
            let eventTypeId = getPageEventType();
            if (notEmpty(seasonId) && notEmpty(competitionId) && notEmpty(eventTypeId)) {
                let totalType = $("#filter-totaltype").val();
                if (notEmpty(totalType)) {
                    params.push("totalType;" + totalType);
                }
                params.push("seasonId;" + seasonId);
                params.push("competitionId;" + competitionId.join("|"));
                let groupIds = $("#filter-groupid").val();
                if (notEmpty(groupIds)) {
                    params.push("groupId;" + groupIds.join("|"));
                }
                params.push("eventTypeId;" + eventTypeId);
                if (filterChanged !== "eventTypeId") {
                    let tagTypeId = getPageTagType();
                    if (notEmpty(tagTypeId)) {
                        tagTypeId.sort((a, b) => Number(a) - Number(b));
                        params.push("tagTypeId;" + tagTypeId.join("|"));
                    }
                }
                var eventTypeIdElements = $("#event-filters-container").find("div.tab-pane.fade.show.active").find("select.additional-filter");
                if (eventTypeIdElements.length > 0) {
                    for (var i = 0; i < eventTypeIdElements.length; i++) {
                        let splitted = $(eventTypeIdElements[i]).attr("id").split("-");
                        let extraIndex = splitted[splitted.length - 1];
                        let isEventType = $(eventTypeIdElements[i]).attr("id").includes("eventtypeid");
                        if (isEventType) {
                            let tmpEventTypeId = $(eventTypeIdElements[i]).val();
                            if (notEmpty(tmpEventTypeId)) {
                                params.push("eventTypeId" + extraIndex + ";" + tmpEventTypeId);
                            }
                        } else {
                            let tmpTagTypeId = $(eventTypeIdElements[i]).val();
                            if (notEmpty(tmpTagTypeId)) {
                                tmpTagTypeId.sort((a, b) => Number(a) - Number(b));
                                params.push("tagTypeId" + extraIndex + ";" + tmpTagTypeId.join("|"));
                            }
                        }
                    }
                }
                let isHomeTeam = $("#filter-ishometeam").val();
                if (notEmpty(isHomeTeam)) {
                    params.push("isHomeTeam;" + isHomeTeam);
                }
                let matchday = matchdaySlider.get();
                params.push("matchdayFrom;" + parseInt(matchday[0]));
                params.push("matchdayTo;" + parseInt(matchday[1]));
                let homeModule = $("#filter-homemodule").val();
                if (notEmpty(homeModule)) {
                    params.push("homeModule;" + homeModule.join("|"));
                }
                let awayModule = $("#filter-awaymodule").val();
                if (notEmpty(awayModule)) {
                    params.push("awayModule;" + awayModule.join("|"));
                }
                // filtro per area oppure filtro per il posizionale
                let area = $("#filter-area").val();
                if (notEmpty(area)) {
                    params.push("area;" + area);
                    // se ho almeno un filtro posizionale dico all'utente che non verranno considerati
                    let half = $("#filter-half").val();
                    let zone = $("#filter-zone").val();
                    let channel = $("#filter-channel").val();
                    if (notEmpty(half) || notEmpty(zone) || notEmpty(channel)) {
                        new Noty({
                            text: "<spring:message code="messages.positional.filter.warn"/>",
                            type: "warning",
                            layout: "topCenter"
                        }).show();
                    }
                } else {
                    let half = $("#filter-half").val();
                    if (notEmpty(half)) {
                        params.push("half;" + half);
                    }
                    let zone = $("#filter-zone").val();
                    if (notEmpty(zone)) {
                        params.push("zone;" + zone);
                    }
                    let channel = $("#filter-channel").val();
                    if (notEmpty(channel)) {
                        params.push("channel;" + channel);
                    }
                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);
                showBlockUI();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/team/ranking/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        $.unblockUI();
                        if (notEmpty(result)) {
                            var data = JSON.parse(result);
//                            console.log(data);

                            if (jQuery.isEmptyObject(data)) {
                                $("#page-title").addClass("d-none");
                                if (params.length === 0) {
                                    showChartMessage(1);
                                } else {
                                    showChartMessage(2);
                                }
                            } else {
                                $(".message-div").addClass("d-none");
                                $("#chartdiv").children("div:not(.message-div)").removeClass("d-none");
                                $("#page-title").removeClass("d-none");
                                $("#page-title").html(getPageTitle());

                                let eventTypeIds = [];
                                eventTypeIds.push(getPageEventType());

                                var eventTypeIdElements = $("#event-filters-container").find("div.tab-pane.fade.show.active").find("select.additional-filter");
                                if (eventTypeIdElements.length > 0) {
                                    for (var i = 0; i < eventTypeIdElements.length; i++) {
                                        let isEventType = $(eventTypeIdElements[i]).attr("id").includes("eventtypeid");
                                        if (isEventType) {
                                            let tmpEventTypeId = $(eventTypeIdElements[i]).val();
                                            if (notEmpty(tmpEventTypeId)) {
                                                eventTypeIds.push(tmpEventTypeId);
                                            }
                                        }
                                    }
                                }

                                if (typeof chart === "undefined") {
                                    getTeamRankingChart(data);
                                } else {
                                    // tolgo le altre serie se ci sono
                                    chart.series.clear();
                                    createTeamRankingAxis(chart, data);
                                }

                                // xAxis.data.setAll(data);
                                eventTypeIds.forEach(function (eventTypeId) {
                                    addTeamRankingSeries(chart, eventTypeId, data, "${mUser.tvLanguage}");
                                });
                                if (eventTypeIds.length > 1) {
                                    if (typeof seriesRangeDataItem !== "undefined" && !seriesRangeDataItem.isDisposed()) {
                                        seriesRangeDataItem.dispose();
                                    }
                                }
                            }
                        }
                        stopChartLoading = false;
                    },
                    error: function () {
                        $.unblockUI();
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }

    var additionalFilterAmount = 0;
    function addAdditionalFilter() {
        additionalFilterAmount++;
        let prefix = getPageEventPrefix();

        $('#filter-' + prefix + 'eventtypeid').select2('destroy');
        $('#filter-' + prefix + 'tagtypeid').multiselect('destroy');

        var clonedSeparator = $("#additional-event-separator").clone();
        clonedSeparator.removeClass("d-none");
        clonedSeparator.removeAttr("id");
        clonedSeparator.find("i").attr("index", additionalFilterAmount);
        $("#event-filters-" + (prefix || "event")).append(clonedSeparator);

        var eventTypeId, tagTypeId;
        var clonedEventTypeId = $("#container-" + prefix + "eventtypeid").clone();
        clonedEventTypeId.addClass("mt-2");
        clonedEventTypeId.attr('id', function (index, id) {
            eventTypeId = id + '-' + additionalFilterAmount;
            return id + '-' + additionalFilterAmount;
        });
        clonedEventTypeId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedEventTypeId.find("span").remove();
        clonedEventTypeId.find("select").attr("onchange", clonedEventTypeId.find("select").attr("onchange").replace("updateFilters('" + prefix + "eventTypeId');", "updateFilters('" + prefix + "eventTypeId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedEventTypeId.find("select").addClass("additional-filter");
        $("#event-filters-" + (prefix || "event")).append(clonedEventTypeId);

        if (prefix !== "advanced") {
            var clonedTagTypeId = $("#container-" + prefix + "tagtypeid").clone();
            clonedTagTypeId.addClass("mt-2");
            clonedTagTypeId.attr('id', function (index, id) {
                tagTypeId = id + '-' + additionalFilterAmount;
                return id + '-' + additionalFilterAmount;
            });
            clonedTagTypeId.find('[id]').each(function () {
                var currentId = $(this).attr('id');
                $(this).attr('id', currentId + '-' + additionalFilterAmount);
            });
            clonedTagTypeId.find("select").attr("onchange", clonedTagTypeId.find("select").attr("onchange").replace("updateFilters('" + prefix + "tagTypeId');", "updateFilters('" + prefix + "tagTypeId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
            clonedTagTypeId.find("select").addClass("additional-filter");
            $("#event-filters-" + (prefix || "event")).append(clonedTagTypeId);
        }

        $("#filter-" + prefix + "eventtypeid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });
        if (prefix !== "advanced") {
            $("#filter-" + prefix + "tagtypeid-" + additionalFilterAmount).multiselect({
                selectAllText: '<spring:message code="filters.select.all"/>',
                enableFiltering: true,
                includeFilterClearBtn: false,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function () {
                    if (typeof this.$filter !== "undefined") {
                        $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                        this.$filter.find('.multiselect-search').focus();
                        this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                    }
                }
            });
        }

        $('#filter-' + prefix + 'eventtypeid').select2({
            dropdownAutoWidth: true
        });
        if (prefix !== "advanced") {
            $('#filter-' + prefix + 'tagtypeid').multiselect({
                selectAllText: '<spring:message code="filters.select.all"/>',
                enableFiltering: true,
                includeFilterClearBtn: false,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function () {
                    if (typeof this.$filter !== "undefined") {
                        $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                        this.$filter.find('.multiselect-search').focus();
                        this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                    }
                }
            });
        }

        // fix css per search
        $('.multiselect-search').css('padding-left', 'calc(0.875rem * 2 + 1.25rem)');
        updateFiltersColor();
        bindFiltersSave();

        if (notEmpty($("#filter-" + prefix + "eventtypeid-" + additionalFilterAmount).val())) {
            $("#filter-eventtypeid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-" + prefix + "tagtypeid-" + additionalFilterAmount).val())) {
            $("#filter-tagtypeid-" + additionalFilterAmount).trigger("custom-change");
        }
    }

    function removeAdditionalFilter() {
        if (typeof event.target !== "undefined") {
            let index = $(event.target).attr("index");
            if (notEmpty(index)) {
                let prefix = getPageEventPrefix();

                $("#container-" + prefix + "eventtypeid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-" + prefix + "eventtypeid-" + index).remove();
                $("#container-" + prefix + "tagtypeid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-" + prefix + "tagtypeid-" + index).remove();
                $(event.target).parent().remove();
                updateChart();
            }
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <div class="sidebar sidebar-secondary sidebar-expand-lg">
            <!-- Expand button -->
            <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                <i class="ph-caret-right"></i>
            </button>
            <!-- /expand button -->

            <!-- Sidebar content -->
            <div class="sidebar-content" id="filters-containter">

                <!-- Header -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                    <div class="me-1">
                        <i class="ph-arrow-counter-clockwise cursor-pointer" title="<spring:message code="filters.reset"/>" onclick="resetFilters();"></i>
                        <i class="ph-funnel cursor-pointer" title="<spring:message code="filters.show.hide"/>" onclick="managePersonalFilters();"></i>
                    </div>
                    <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex">
                            <i class="ph-arrows-left-right"></i>
                        </button>

                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                            <i class="ph-x"></i>
                        </button>
                    </div>
                </div>
                <!-- /header -->

                <!-- Header Filter Message -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center justify-content-center mb-0 mt-0 py-1 d-none" id="specific-filter-message">
                    <div>
                        <i class="ph-warning text-warning"></i><span class="ms-1 text-warning"><spring:message code="filters.specific.warning"/></span>
                    </div>
                </div>
                <!-- /header filter message -->

                <!-- Sidebar search -->
                <div class="sidebar-section">
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-4 d-none" id="personal-filters-container">
                            <label class="form-label mb-0"><spring:message code="personal.filters"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="personal.filters.placeholder"/>" class="form-control form-control-sm select" id="personal-filter" onchange="loadPersonalFilter();" data-minimum-results-for-search="Infinity" data-width="1%">
                                        <option value=""></option>
                                        <c:forEach var="filter" items="${mFilters}">
                                            <option value="${filter.id}">${filter.name}</option>
                                        </c:forEach>
                                    </select>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" id="personal-filter-update" onclick="updatePersonalFilter();" title="<spring:message code="personal.filter.save.placeholder"/>" disabled>
                                        <i class="ph-pencil"></i>
                                    </button>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" onclick="addPersonalFilter();" title="<spring:message code="personal.filter.add.placeholder"/>">
                                        <i class="ph-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.season"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.season.placeholder"/>" class="form-control form-control-sm select is-filter is-required update-groupid" id="filter-seasonid" onchange="updateFilters('seasonId');" data-minimum-results-for-search="Infinity">
                                    <c:forEach var="season" items="${mSeasons}">
                                        <option value="${season.id}">${season.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required update-groupid" id="filter-competitionid" onchange="updateFilters('competitionId');">
                                </select>
                            </div>
                        </div>
                        <div class="mb-2 d-none" id="filter-groupid-container">
                            <label class="form-label mb-0"><spring:message code="filters.group"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.group.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-initial-load" id="filter-groupid" onchange="updateFilters('groupId');">
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.displayed"/></label>
                            <div class="form-control-feedback form-control-feedback-end">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.displayed.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-totaltype" onchange="updateChart('totalType');" data-minimum-results-for-search="Infinity">
                                    <option value="totals" selected><spring:message code="filters.displayed.totals"/></option>
                                    <option value="p90"><spring:message code="filters.displayed.p90"/></option>
                                    <option value="touches"><spring:message code="filters.displayed.touches"/></option>
                                    <option value="average"><spring:message code="filters.displayed.average"/></option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 px-0 py-0" action="#" id="event-filters-container">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="event-container-nav">
                            <li class="nav-item">
                                <a href="#event-filters-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-advanced" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-tactical" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-3">
                            <div class="tab-pane fade show active" id="event-filters-event">
                                <div class="mb-2" id="container-eventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra eventtypeid" id="filter-eventtypeid" onchange="updateFilters('eventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tagtypeid" onchange="updateFilters('tagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-advanced">
                                <div class="mb-2" id="container-advancedeventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-advancedeventtypeid" onchange="updateFilters('advancedeventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-tactical">
                                <div class="mb-2" id="container-tacticaleventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-tacticaleventtypeid" onchange="updateFilters('tacticaleventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tacticaltagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tacticaltagtypeid" onchange="updateFilters('tacticaltagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center mt-3 d-none" id="additional-event-separator">
                            <div class="hr"></div>
                            <i class="ph-trash cursor-pointer mx-1" onclick="removeAdditionalFilter();"></i>
                            <div class="hr"></div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mt-3" id="add-event-button">
                            <div class="bg-info bg-opacity-20 rounded-pill text-info p-2 cursor-pointer" onclick="addAdditionalFilter();">
                                <i class="ph-plus"></i>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.match.details"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.away"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.away.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-ishometeam" onchange="updateFilters('isHomeTeam');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.matchday"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-homemodule" onchange="updateFilters('homeModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.away.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.away.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-awaymodule" onchange="updateFilters('awayModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.positional"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.half"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.half.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-half" onchange="updateChart('half');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.zone"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.zone.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-zone" onchange="updateChart('zone');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.channel"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.channel.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-channel" onchange="updateChart('channel');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.area"/> <i class="ph-info" data-bs-popup="tooltip" data-bs-placement="top" data-bs-html="true" title="<spring:message code="filters.area.tooltip"/>"></i></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.area.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-area" onchange="updateChart('area');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->
            </div>
            <!-- /sidebar content -->
        </div>

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content" id="container">
                    <div class="row">
                        <h6 class="mb-1 text-center" id="page-title"></h6>
                    </div>
                    <div class="row">
                        <div class="amchart" id="chartdiv">
                            <%@ include file="../global/messages.jsp" %>
                        </div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
