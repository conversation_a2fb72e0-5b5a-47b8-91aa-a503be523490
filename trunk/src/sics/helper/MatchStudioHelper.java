package sics.helper;

import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.font.TextAttribute;
import java.awt.geom.*;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.awt.image.ImagingOpException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.text.AttributedString;
import java.util.*;
import java.util.List;
import javax.imageio.ImageIO;

import jdk.nashorn.internal.objects.Global;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.imgscalr.Scalr;
import sics.domain.*;
import sics.domain.Event;
import sics.domain.Point;
import sics.domain.matchstudio.ArrowHead;

public class MatchStudioHelper {

    private static BufferedImage kLine = null;
    private static BufferedImage kLineNoCentro = null;
    private static BufferedImage kGrass2023 = null;
    private static BufferedImage kPorta2023 = null;
    private static BufferedImage kCloseMedium = null;
    private static BufferedImage kBallMedium = null;
    private static BufferedImage kIconReportSostituzione = null;
    private static BufferedImage kIconReportSubentro = null;

    public static final int moltFieldBig = 7;

    public static int moltCampo = 4;
    protected static int mFontSize = 17;
    protected static int mFontSizeBig = 25; // usato per pagina giocatori

    public static String kServletContextPathImages = null;

    public static void initialize() {
        try {
            kLine = ImageIO.read(new File(kServletContextPathImages + "matchstudio/campo.png"));
            kLineNoCentro = ImageIO.read(new File(kServletContextPathImages + "matchstudio/campoNoCentro.png"));
            kGrass2023 = ImageIO.read(new File(kServletContextPathImages + "matchstudio/campo_2023.png"));
            kPorta2023 = ImageIO.read(new File(kServletContextPathImages + "matchstudio/porta_2023.png"));
            kCloseMedium = ImageIO.read(new File(kServletContextPathImages + "matchstudio/close-cross24.png"));
            kBallMedium = ImageIO.read(new File(kServletContextPathImages + "matchstudio/ball24.png"));
            kIconReportSostituzione = ImageIO.read(new File(kServletContextPathImages + "matchstudio/report-sostituzione.png"));
            kIconReportSubentro = ImageIO.read(new File(kServletContextPathImages + "matchstudio/report-subentro.png"));

            MatchStudioHeatMap.initialize();
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static BufferedImage drawEmptyHorizontalField(int latoLungo, int latoCorto) {
        BufferedImage HorizontalField = null;
        try {
            //creo un campo vuoto orizzontale
            HorizontalField = new BufferedImage(latoLungo, latoCorto, kGrass2023.getType());
            Graphics2D fieldGraphics = (Graphics2D) HorizontalField.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            // DAFARE usare scrlib
            // campo nuovo 2023
            fieldGraphics.drawImage(kGrass2023.getScaledInstance(latoLungo, latoCorto, BufferedImage.SCALE_DEFAULT), 0, 0, null);
            //disegno linee bianche
            fieldGraphics.drawImage(Scalr.resize(kLine, Scalr.Method.AUTOMATIC, Scalr.Mode.FIT_EXACT, latoLungo - 5, latoCorto - 4), 2, 2, null);
        } catch (ImagingOpException | IllegalArgumentException ex) {
            GlobalHelper.reportError(ex);
        }
        return HorizontalField;
    }

    private static BufferedImage drawEmptyHorizontalFieldNoCentro(int latoLungo, int latoCorto) {
        BufferedImage HorizontalField = null;
        try {
            //creo un campo vuoto orizzontale
            HorizontalField = new BufferedImage(latoLungo, latoCorto, kGrass2023.getType());
            Graphics2D fieldGraphics = (Graphics2D) HorizontalField.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            // campo nuovo 2023
            fieldGraphics.drawImage(kGrass2023.getScaledInstance(latoLungo, latoCorto, BufferedImage.SCALE_DEFAULT), 0, 0, null);
            //disegno linee bianche
            fieldGraphics.drawImage(Scalr.resize(kLineNoCentro, Scalr.Method.AUTOMATIC, Scalr.Mode.FIT_EXACT, latoLungo - 5, latoCorto - 4), 2, 2, null);
        } catch (ImagingOpException | IllegalArgumentException ex) {
            GlobalHelper.reportError(ex);
        }
        return HorizontalField;
    }

    public static BufferedImage drawEmptyVerticalField(int latoCorto, int latoLungo) {
        //creo un campo vuoto orizzontale
        BufferedImage verticalField = null;
        try {
            BufferedImage HorizontalField = drawEmptyHorizontalField(latoLungo, latoCorto);

            if (latoLungo == EventHelper.kLatoLungoCampo) {
                File fieldOut = new File("fieldHorizontal.png");
                try {
                    ImageIO.write(HorizontalField, "png", fieldOut);

                } catch (IOException ex) {
                    GlobalHelper.reportError(ex);
                }
            }

            verticalField = new BufferedImage(latoCorto, latoLungo, HorizontalField.getType());
            for (int x = 0; x < latoLungo; x++) {
                for (int y = 0; y < latoCorto; y++) {
                    verticalField.setRGB(y, latoLungo - x - 1, HorizontalField.getRGB(x, y));
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return verticalField;
    }

    private static BufferedImage drawEmptyVerticalFieldNoCentro(int latoCorto, int latoLungo) {
        //creo un campo vuoto orizzontale
        BufferedImage verticalField = null;
        try {
            BufferedImage HorizontalField = drawEmptyHorizontalFieldNoCentro(latoLungo, latoCorto);

            if (latoLungo == EventHelper.kLatoLungoCampo) {
                File fieldOut = new File("fieldHorizontal.png");
                try {
                    ImageIO.write(HorizontalField, "png", fieldOut);
                } catch (IOException ex) {
                    GlobalHelper.reportError(ex);
                }
            }

            verticalField = new BufferedImage(latoCorto, latoLungo, HorizontalField.getType());
            for (int x = 0; x < latoLungo; x++) {
                for (int y = 0; y < latoCorto; y++) {
                    verticalField.setRGB(y, latoLungo - x - 1, HorizontalField.getRGB(x, y));
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return verticalField;
    }

    private static void drawCanaliLines(BufferedImage field, int numeroCanali, boolean includeLabels) {
        try {
            int halfCanale = moltFieldBig * (int) Math.round((double) EventHelper.kLatoCortoCampo / (double) numeroCanali) / 2;
            Font font = new Font("Roboto", Font.BOLD, 30);
            for (int i = 1; i <= numeroCanali; i++) {
                Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                int x1 = moltFieldBig * (int) Math.round(((double) EventHelper.kLatoCortoCampo / (double) numeroCanali) * i);
                if (i != numeroCanali) {
                    int x2 = x1;
                    int y1 = 0;
                    int y2 = moltFieldBig * EventHelper.kLatoLungoCampo;

                    BasicStroke bs = new BasicStroke(2, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 5, 5}, 7);
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                    fieldGraphics.setStroke(bs);
                    fieldGraphics.setPaint(Color.WHITE);
                    fieldGraphics.drawLine(x1, y1, x2, y2);
                }
                if (includeLabels) {
                    fieldGraphics.setFont(font);
                    fieldGraphics.setColor(Color.WHITE);
                    fieldGraphics.drawString("C" + i, x1 - halfCanale - 20, 25);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static void drawTerziLines(BufferedImage field, int numeroTerzi) {
        try {
            for (int i = 1; i < numeroTerzi; i++) {
                Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                int x1 = 0;
                int x2 = moltFieldBig * EventHelper.kLatoCortoCampo;
                int y1 = moltFieldBig * (int) Math.round(((double) EventHelper.kLatoLungoCampo / (double) numeroTerzi) * i);
                int y2 = y1;

                BasicStroke bs = new BasicStroke(2, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 5, 5}, 7);
                fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                fieldGraphics.setStroke(bs);
                fieldGraphics.setPaint(Color.WHITE);
                fieldGraphics.drawLine(x1, y1, x2, y2);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static Point scalaSuFieldBig(double posX, double posY) {
        Point p = null;
        try {
            int w = EventHelper.kLatoCortoCampo * moltFieldBig;
            int h = EventHelper.kLatoLungoCampo * moltFieldBig;
            int wCampo = EventHelper.kLatoCortoCampo * moltFieldBig - 4;
            int hCampo = EventHelper.kLatoLungoCampo * moltFieldBig - 6;
            double x = (posX * wCampo / w + 2);
            double y = (posY * hCampo / h + 4);
            p = new Point(x, y);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return p;
    }

    private static Point getPuntoScalatoPorta2023(Point point) {
        point.setX(point.getX() / moltFieldBig);
        point.setY(point.getY() / moltFieldBig);
        double puntoZeroOldPorta = (EventHelper.kLatoCortoCampo - EventHelper.mLarghezzaPorta) / 2;    // 30.34
        // double puntoFinaleOldPorta = puntoZeroOldPorta + mLarghezzaPorta;   // 37.66

        // Se il punto è a X = 31, il puntoPartendoDaZero sarà 0.66
        double puntoXPartendoDaZero = point.getX() - puntoZeroOldPorta;
        boolean scalaX = true;
        boolean scalaY = true;

        if (puntoXPartendoDaZero < 0) { // punto fuori porta a DESTRA
            point.setX(1D * EventHelper.pixelLarghezzaPorta2023 + EventHelper.pixelLatiPorta2023 + (EventHelper.pixelLatiPorta2023 / 2)); // vado a destra di pixelLatiPorta2023 / 2 px dopo la fine della porta
            scalaX = false;
        } else if (puntoXPartendoDaZero > EventHelper.mLarghezzaPorta) { // punto fuori porta a SINISTRA
            point.setX(1D * EventHelper.pixelLatiPorta2023 / 2); // pixelLatiPorta2023 / 2 è la metà della dimensione dei lati della porta
            scalaX = false;
        }
        if (point.getY() > EventHelper.mAltezzaPorta) { // punto fuori porta SOPRA
            point.setY(1D * EventHelper.pixelLatiPorta2023 / 2);
            scalaY = false;
        }

        if (scalaX) {
            point.setX(1D * (EventHelper.pixelLarghezzaPorta2023 + EventHelper.pixelLatiPorta2023) - puntoXPartendoDaZero / EventHelper.mLarghezzaPorta * EventHelper.pixelLarghezzaPorta2023);
        }
        if (scalaY) {
            point.setY(1D * (EventHelper.pixelAltezzaPorta2023 + EventHelper.pixelLatiPorta2023) - (point.getY() / EventHelper.mAltezzaPorta * EventHelper.pixelAltezzaPorta2023));
        }

        return point;
    }

    public static void centraPalliniTagliati(BufferedImage field, Point p, int ellipseSize) {
        try {
            if (p.getX() > (field.getWidth() - ellipseSize / 2)) {
                p.setX(1D * field.getWidth() - ellipseSize / 2);
            }
            if (p.getX() < ellipseSize / 2) {
                p.setX(1D * ellipseSize / 2);
            }
            if (p.getY() > field.getHeight() - ellipseSize / 2) {
                p.setY(1D * field.getHeight() - ellipseSize / 2);
            }
            if (p.getY() < ellipseSize / 2) {
                p.setY(1D * ellipseSize / 2);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    protected static Point centraPuntoTagliatoPorta2023(Point point, int size) {
        /*
        // dimensioni reali porta
        public static final double mAltezzaPorta = 2.44f;
        public static final double mLarghezzaPorta = 7.32f;
        public static final int pixelLatiPorta2023 = 100;
        public static final int pixelLarghezzaPorta2023 = 1500;
        public static final int pixelAltezzaPorta2023 = 600;
        public static final int moltFieldBig = 7;
         */

        double xMaxValue = (EventHelper.pixelLarghezzaPorta2023 + (EventHelper.pixelLatiPorta2023 * 2) - (size / 2)); // 1700 + (size / 2)
        if (point.getX() > xMaxValue) {
            double valueToRemove = xMaxValue - point.getX();
            point.setX(point.getX() + valueToRemove);
        }
        double xMinValue = (size / 2); // (size / 2)
        if (point.getX() < xMinValue) {
            double valueToAdd = xMinValue - point.getX();
            point.setX(point.getX() - valueToAdd);
        }

        double yMaxValue = (EventHelper.pixelAltezzaPorta2023 + EventHelper.pixelLatiPorta2023 - (size / 2)); // 700 + (size / 2)
        if (point.getY() > yMaxValue) {
            double valueToRemove = yMaxValue - point.getY();
            point.setY(point.getY() + valueToRemove);
        }
        double yMinValue = (size / 2); // (size / 2)
        if (point.getY() < yMinValue) {
            double valueToAdd = yMinValue - point.getY();
            point.setY(point.getY() - valueToAdd);
        }

        return point;
    }

    private static void disegnaPallini(BufferedImage field, Point p, int size, Color color) {
        disegnaPallini(field, p.getX(), p.getY(), size, color);
    }

    private static void disegnaPallini(BufferedImage field, double x, double y, double size, Color color) {
        try {
            Ellipse2D circle = new Ellipse2D.Double(x - (size / 2), y - (size / 2), size, size);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            BasicStroke bs = new BasicStroke(0);
            fieldGraphics.setStroke(bs);
            fieldGraphics.setPaint(color);
            fieldGraphics.fill(circle);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static void disegnaPalliniTrasparenti(BufferedImage field, double x, double y, double size) {
        try {
            Ellipse2D circle = new Ellipse2D.Double(x - (size / 2), y - (size / 2), size, size);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.setColor(Color.BLACK);
            fieldGraphics.draw(circle);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static void disegnaPalliniTimeline(BufferedImage field, int x, int y, int size, BufferedImage imgPallino) {
        try {
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.drawImage(imgPallino, x - size / 2, y - size / 2, null);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static void disegnaCroce(BufferedImage field, double x, double y, int size) {
        try {
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.drawImage(kCloseMedium, (int) Math.round(x - (size / 2)), (int) Math.round(y - (size / 2)), size, size, null);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static void disegnaPallone(BufferedImage field, double x, double y, int size) {
        try {
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.drawImage(kBallMedium, (int) Math.round(x - (size / 2)), (int) Math.round(y - (size / 2)), size, size, null);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    private static double angleBetween(Shape from, Shape to) {
        return angleBetween(center(from.getBounds2D()), center(to.getBounds2D()));
    }

    private static double angleBetween(Point2D from, Point2D to) {
        double x = from.getX();
        double y = from.getY();

        // This is the difference between the anchor point
        // and the mouse.  Its important that this is done
        // within the local coordinate space of the component,
        // this means either the MouseMotionListener needs to
        // be registered to the component itself (preferably)
        // or the mouse coordinates need to be converted into
        // local coordinate space
        double deltaX = to.getX() - x;
        double deltaY = to.getY() - y;

        // Calculate the angle...
        // This is our "0" or start angle..
        double rotation = -Math.atan2(deltaX, deltaY);
        rotation = Math.toRadians(Math.toDegrees(rotation) + 180);

        return rotation;
    }

    protected static Point2D center(Rectangle2D bounds) {
        return new Point2D.Double(bounds.getCenterX(), bounds.getCenterY());
    }

    private static Point2D getPointOnCircle(Shape shape, double radians) {
        Rectangle2D bounds = shape.getBounds();
        Point2D point = center(bounds);
        return getPointOnCircle(point, radians, Math.max(bounds.getWidth(), bounds.getHeight()) / 2d);
    }

    private static Point2D getPointOnCircle(Point2D center, double radians, double radius) {
        double x = center.getX();
        double y = center.getY();

        radians = radians - Math.toRadians(90.0); // 0 becomes the top
        // Calculate the outter point of the line
        double xPosy = Math.round((float) (x + Math.cos(radians) * radius));
        double yPosy = Math.round((float) (y + Math.sin(radians) * radius));

        return new Point2D.Double(xPosy, yPosy);
    }

    protected static void drawMediaLine(BufferedImage field, int moltCampo, float sommaAltezze, int numeroAzioni) {
        try {
            if (numeroAzioni > 0) {
                Font fontone = new Font("Roboto", Font.PLAIN, mFontSizeBig);
                int altezzaMediaCampo = Math.round(sommaAltezze * moltCampo / numeroAzioni);
                BasicStroke bs = new BasicStroke(2, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 5, 5}, 7);
                Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                fieldGraphics.setStroke(bs);
                fieldGraphics.setPaint(Color.WHITE);
                fieldGraphics.drawLine(0, altezzaMediaCampo, EventHelper.kLatoCortoCampo * moltCampo, altezzaMediaCampo);
                int altezzaMedia = Math.round(EventHelper.kLatoLungoCampo - (sommaAltezze / numeroAzioni));
                fieldGraphics.setFont(fontone.deriveFont(Font.BOLD, 25));
                fieldGraphics.drawString(String.valueOf(altezzaMedia) + "m", 8, altezzaMediaCampo - 6);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static BufferedImage rotateImageBy(BufferedImage source, double degrees) {
        // The size of the original image
        int w = source.getWidth();
        int h = source.getHeight();
        // The angel of the rotation in radians
        double rads = Math.toRadians(degrees);
        // Some nice math which demonstrates I have no idea what I'm talking about
        // Okay, this calculates the amount of space the image will need in
        // order not be clipped when it's rotated
        double sin = Math.abs(Math.sin(rads));
        double cos = Math.abs(Math.cos(rads));
        int newWidth = (int) Math.floor(w * cos + h * sin);
        int newHeight = (int) Math.floor(h * cos + w * sin);

        // A new image, into which the original can be painted
        BufferedImage rotated = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = rotated.createGraphics();
        // The transformation which will be used to actually rotate the image
        // The translation, actually makes sure that the image is positioned onto
        // the viewable area of the image
        AffineTransform at = new AffineTransform();
        at.translate((newWidth - w) / 2, (newHeight - h) / 2);

        // And we rotate about the center of the image...
        int x = w / 2;
        int y = h / 2;
        at.rotate(rads, x, y);
        g2d.setTransform(at);
        // And we paint the original image onto the new image
        g2d.drawImage(source, 0, 0, null);
        g2d.dispose();

        return rotated;
    }

    public static Path2D.Double closeFreccia(Integer width, Integer height, Point p0, Point p1, float shiftPunta, int lunghFreccia, int largFreccia) {

        Path2D.Double triangle = new Path2D.Double();
        try {

            Integer lunghezzafreccia = 13;
            Integer larghezzafreccia = 8;
            if (lunghFreccia != 0) {
                lunghezzafreccia = lunghFreccia;
            }
            if (largFreccia != 0) {
                larghezzafreccia = largFreccia;
            }
            Double xstart = p0.getX();
            Double ystart = p0.getY();
            Double xend = p1.getX();
            Double yend = p1.getY();

            double xA = 0d;
            double xB = 0d;
            //(-b + Math.Sqrt(delta)) / (2 * a)
            double yA = 0d; //(m2 * xA) + q2
            double yB = 0d; //(m2 * xB) + q2

            //RETTA 1 corpo freccia
            double m1 = (ystart - yend) / (xstart - xend);
            double q1 = ystart - (m1 * xstart);
            //Punto Finale //Medio
            double xM = xend; //((xEnd - xStart) / 2) + xStart
            double yM = yend; //((yEnd - yStart) / 2) + yStart

            // mantiene il punto finale all'interno del panel con dimensioni width, height
            //y = m1x + q1
            if (xend < 0) {
                xend = 0d;
                yend = q1;
            } else if (xend > width) {
                xend = width.doubleValue();
                yend = m1 * xend + q1;
            }
            if (yend < 0) {
                yend = 0d;
                xend = -q1 / m1;

            } else if (yend > height) {
                yend = height.doubleValue();
                xend = (yend - q1) / m1;
            }

            //coordinate vertice triangolo
            Double xP = xend;
            Double yP = yend;

            Boolean orizzontale = ystart.equals(yend);
            Boolean verticale = xstart.equals(xend);

            if (shiftPunta != 0) {
                if (orizzontale) {
                    if (xend < xstart) {
                        xP = xP + shiftPunta;
                    } else {
                        xP = xP - shiftPunta;
                    }
                } else if (verticale) {
                    if (yend < ystart) {
                        yP = yP + shiftPunta;
                    } else {
                        yP = yP - shiftPunta;
                    }
                } else {
                    //Risoluzione Sistema per trovare xP, yP punto finale del triangolo (punto elemento della retta con distanza da xEnd,yEnd = raggioPlayer)

                    double aP = (Math.pow(m1, 2)) + 1;
                    double bP = (2 * m1 * q1) - (2 * xend) - (2 * m1 * yend);
                    double cP = (Math.pow(q1, 2)) - (2 * q1 * yend) + (Math.pow(xend, 2)) + (Math.pow(yend, 2)) - (Math.pow(shiftPunta, 2));
                    double deltaP = Math.pow(bP, 2) - (4 * aP * cP);

                    double xp1;
                    double xp2;
                    double yp1;
                    double yp2;

                    xp1 = (-bP - Math.sqrt(deltaP)) / (2 * aP);
                    xp2 = (-bP + Math.sqrt(deltaP)) / (2 * aP);
                    yp1 = (m1 * xp1) + q1;
                    yp2 = (m1 * xp2) + q1;

                    if (shiftPunta > 0) {
                        // se lo shift è positivo la punta arretra lungo il segmento
                        if (xstart > xend) {
                            if (xp1 == Math.min(xp1, xp2)) {
                                xP = xp2;
                                yP = yp2;
                            } else {
                                xP = xp1;
                                yP = yp1;
                            }
                        } else if (xp1 == Math.min(xp1, xp2)) {
                            xP = xp1;
                            yP = yp1;
                        } else {
                            xP = xp2;
                            yP = yp2;
                        }
                    } else // se lo shift è negativo la punta avanza oltre il segmento
                    {
                        if (xstart > xend) {
                            if (xp1 == Math.min(xp1, xp2)) {
                                xP = xp1;
                                yP = yp1;
                            } else {
                                xP = xp2;
                                yP = yp2;
                            }
                        } else if (xp1 == Math.min(xp1, xp2)) {
                            xP = xp2;
                            yP = yp2;
                        } else {
                            xP = xp1;
                            yP = yp1;
                        }
                    }
                }
            }

            // mantiene la punta freccia all'interno del panel con dimensioni width, height
            //y = m1x + q1
            if (xP < 0) {
                xP = 0d;
                yP = q1;
            } else if (xP > width) {
                xP = width.doubleValue();
                yP = m1 * xP + q1;
            }
            if (yP < 0) {
                yP = 0d;
                if (!verticale) {
                    xP = -q1 / m1;
                }
            } else if (yP > height) {
                yP = height.doubleValue();
                if (!verticale) {
                    xP = (yP - q1) / m1;
                }
            }

            if (orizzontale) {
                if (xend < xstart) {
                    xA = xP + lunghezzafreccia;
                } else {
                    xA = xP - lunghezzafreccia;
                }
                xB = xA;
                yA = yP + larghezzafreccia;
                yB = yP - larghezzafreccia;
            } else if (verticale) {
                if (yend < ystart) {
                    yA = yP + lunghezzafreccia;
                } else {
                    yA = yP - lunghezzafreccia;
                }
                yB = yA;
                xA = xP + larghezzafreccia;
                xB = xP - larghezzafreccia;
            } else {
                //Risoluzione Sistema per trovare xM, yM punto medio della base del triangolo (punto elementi della retta con distanza da xEnd,yEnd = Dist)
                double aC = (Math.pow(m1, 2)) + 1;
                double bC = (2 * m1 * q1) - (2 * xP) - (2 * m1 * yP);
                double cC = (Math.pow(q1, 2)) - (2 * q1 * yP) + (Math.pow(xP, 2)) + (Math.pow(yP, 2)) - (Math.pow(lunghezzafreccia, 2));
                double deltaC = Math.pow(bC, 2) - (4 * aC * cC);

                double xc1;
                double xc2;
                double yc1;
                double yc2;

                xc1 = (-bC - Math.sqrt(deltaC)) / (2 * aC);
                xc2 = (-bC + Math.sqrt(deltaC)) / (2 * aC);
                yc1 = (m1 * xc1) + q1;
                yc2 = (m1 * xc2) + q1;

                if (xstart > xend) {
                    if (xc1 == Math.min(xc1, xc2)) {
                        xM = xc2;
                        yM = yc2;
                    } else {
                        xM = xc1;
                        yM = yc1;
                    }
                } else if (xc1 == Math.min(xc1, xc2)) {
                    xM = xc1;
                    yM = yc1;
                } else {
                    xM = xc2;
                    yM = yc2;
                }

                //RETTA 2 perpendicolare alla punta
                double m2 = -1 / m1; //rette ortogonali
                double q2 = yM - (m2 * xM);

                //Risoluzione Sistema per trovare Xa, Ya e Xb,Yb (punti elementi della retta ortogonale con distanza da xEnd,yEnd = Dist
                double a = (m2 * m2) + 1;
                double b = (2 * m2 * q2) - (2 * xM) - (2 * m2 * yM);
                double c = (q2 * q2) - (2 * q2 * yM) + (xM * xM) + (yM * yM) - (larghezzafreccia * larghezzafreccia);
                double delta = b * b - (4 * a * c);

                xA = (-b - Math.sqrt(delta)) / (2 * a);
                xB = (-b + Math.sqrt(delta)) / (2 * a);
                yA = (m2 * xA) + q2;
                yB = (m2 * xB) + q2;

                if (Math.abs(yA - yM) > larghezzafreccia/*8*/) {
                    if (yA > yM) {
                        yA = yM + larghezzafreccia/*8*/;
                    } else {
                        yA = yM - larghezzafreccia/*8*/;
                    }
                }

                if (Math.abs(yB - yM) > larghezzafreccia) {
                    if (yB > yM) {
                        yB = yM + larghezzafreccia;
                    } else {
                        yB = yM - larghezzafreccia;
                    }
                }
            }

            triangle.moveTo(xA, yA);
            triangle.lineTo(xB, yB);
            triangle.lineTo(xP, yP);
            triangle.closePath();
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return triangle;
    }

    protected static List<Point> drawNumber(BufferedImage field, int moltiplicatore, String number, int ellipseSize, int stringXDim, int stringYDim, double x, double y, List<Point> puntiOccupati, List<Point> puntiOccupatiPallini) {
        return drawNumber(field, moltiplicatore, number, ellipseSize, stringXDim, stringYDim, x, y, puntiOccupati, puntiOccupatiPallini, mFontSize, 0);
    }

    protected static List<Point> drawNumber(BufferedImage field, int moltiplicatore, String number, int ellipseSize, int stringXDim, int stringYDim, double x, double y, List<Point> puntiOccupati, List<Point> puntiOccupatiPallini, int fontSize) {
        return drawNumber(field, moltiplicatore, number, ellipseSize, stringXDim, stringYDim, x, y, puntiOccupati, puntiOccupatiPallini, fontSize, 0);
    }

    protected static List<Point> drawNumber(BufferedImage field, int moltiplicatore, String number, int ellipseSize, int stringXDim, int stringYDim, double x, double y, List<Point> puntiOccupati, List<Point> puntiOccupatiPallini, int fontSize, int rotation) {
        try {
            boolean libero = true;
            Point sinistra;
            Point basso;
            Point alto;
            Point destra = new Point(x + (ellipseSize / 2) + 2, y + (stringYDim / 2));
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            Font font = new Font("Roboto", Font.BOLD, fontSize + 5);
            AffineTransform affinetransform = new AffineTransform();
            FontRenderContext frc = new FontRenderContext(affinetransform, true, true);
            int textwidth = (int) (font.getStringBounds(number, frc).getWidth());
            int textheight = (int) (font.getStringBounds(number, frc).getHeight());
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            fieldGraphics.setPaint(Color.BLACK);
            fieldGraphics.setFont(font);
            stringXDim = textwidth;
            stringYDim = textheight;
            if (Integer.parseInt(number) > 10) {
                sinistra = new Point(x - (ellipseSize / 2) - stringXDim - 1, y + (stringYDim / 2));
                basso = new Point(x - (stringXDim / 2), y + (ellipseSize / 2) + stringYDim + 2);
                alto = new Point(x - (stringXDim / 2), y - (ellipseSize / 2) - 2);
            } else {
                sinistra = new Point(x - (ellipseSize / 2) - (stringXDim / 2), y + (stringYDim / 2));
                basso = new Point(x - (stringXDim / 4), y + (ellipseSize / 2) + stringYDim + 2);
                alto = new Point(x - (stringXDim / 4), y - (ellipseSize / 2) - 2);
            }

            // gestione ruotazione testo
            if (rotation != 0) {
                AffineTransform affineTransform = new AffineTransform();
                affineTransform.rotate(Math.toRadians(rotation), 0, 0);
                Font rotatedFont = font.deriveFont(affineTransform);
                fieldGraphics.setFont(rotatedFont);
            }

            //se il primo punto è sul bordo lo stampa sempre a destra
            if (puntiOccupati.isEmpty() && destra.getX() < EventHelper.kLatoCortoCampo * moltCampo - stringXDim) {
                fieldGraphics.drawString(number, destra.getX().floatValue(), destra.getY().floatValue());
                puntiOccupati.add(destra);
                return puntiOccupati;
            }
            if (puntiOccupati.isEmpty() && sinistra.getX() > 0 + stringXDim) {
                fieldGraphics.drawString(number, sinistra.getX().floatValue(), sinistra.getY().floatValue());
                puntiOccupati.add(sinistra);
                return puntiOccupati;
            }

            //controlla se lo spazio alla destra del pallino è libero per stampare il numero
            if (destra.getX() > (EventHelper.kLatoCortoCampo * moltCampo - stringXDim)) {
                libero = false;
            }
            for (int i = 0; (i < puntiOccupati.size() && libero == true); i++) {
                libero = confrontaPunti(destra, puntiOccupati.get(i), stringXDim, stringYDim);
                if (libero == true && i < puntiOccupatiPallini.size()) {
                    libero = confrontaPallini(destra, puntiOccupatiPallini.get(i), stringXDim, stringYDim, ellipseSize);
                }
            }
            if (libero == true) {
                fieldGraphics.drawString(number, destra.getX().floatValue(), destra.getY().floatValue());
                puntiOccupati.add(destra);
                return puntiOccupati;
            }
            libero = true;

            //controlla se lo spazio alla sinistra del pallino è libero per stampare il numero
            if (sinistra.getX() < (0 + stringXDim)) {
                libero = false;
            }
            for (int i = 0; (i < puntiOccupati.size() && libero == true); i++) {
                libero = confrontaPunti(sinistra, puntiOccupati.get(i), stringXDim, stringYDim);
                if (libero == true && i < puntiOccupatiPallini.size()) {
                    libero = confrontaPallini(sinistra, puntiOccupatiPallini.get(i), stringXDim, stringYDim, ellipseSize);
                }
            }
            if (libero == true) {
                //fieldGraphics.setPaint(Color.RED);
                fieldGraphics.drawString(number, sinistra.getX().floatValue(), sinistra.getY().floatValue());
                puntiOccupati.add(sinistra);
                return puntiOccupati;
            }
            libero = true;
            //controlla se lo spazio sotto al pallino è libero per stampare il numero
            if (basso.getY() < (EventHelper.kLatoLungoCampo * moltCampo - stringYDim)) {
                libero = false;
            }
            for (int i = 0; (i < puntiOccupati.size() && libero == true); i++) {
                libero = confrontaPunti(basso, puntiOccupati.get(i), stringXDim, stringYDim);
                if (libero == true && i < puntiOccupatiPallini.size()) {
                    libero = confrontaPallini(basso, puntiOccupatiPallini.get(i), stringXDim, stringYDim, ellipseSize);
                }
            }
            if (libero == true) {
                fieldGraphics.drawString(number, basso.getX().floatValue(), basso.getY().floatValue());
                puntiOccupati.add(basso);
                return puntiOccupati;
            }
            //controlla se lo spazio sopra al pallino è libero per stampare il numero
            //se il punto si trova sul margine in alto lo stampo sotto al pallino
            if (alto.getY() < (0 + stringYDim)) {
                fieldGraphics.drawString(number, basso.getX().floatValue(), basso.getY().floatValue());
                puntiOccupati.add(basso);
                return puntiOccupati;
            }
            fieldGraphics.drawString(number, alto.getX().floatValue(), alto.getY().floatValue());
            puntiOccupati.add(alto);
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return puntiOccupati;

    }

    protected static boolean confrontaPunti(Point newPoint, Point oldPoint, int stringXDim, int stringYDim) {
        try {
            //se x del punto vecchio è compresa nello spazio delle x del punto nuovo
            if (oldPoint.getX() > newPoint.getX() - stringXDim && oldPoint.getX() < newPoint.getX() + stringXDim) {
                //se y del punto vecchio è compresa nello spazio delle y del punto nuovo
                if (oldPoint.getY() > newPoint.getY() - stringYDim && oldPoint.getY() < newPoint.getY() + stringYDim) {
                    //da controllare se non è fuori dai bordi
                    return false;
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return true;
    }

    protected static boolean confrontaPallini(Point newPoint, Point oldPoint, int stringXDim, int stringYDim, int ellipseSize) {
        try {//se x del punto vecchio è compresa nello spazio delle x del punto nuovo
            if (oldPoint.getX() > newPoint.getX() - ellipseSize && oldPoint.getX() < newPoint.getX() + stringXDim) {
                //se y del puntkreporto vecchio è compresa nello spazio delle y del punto nuovo
                if (oldPoint.getY() > newPoint.getY() - stringYDim - ellipseSize && oldPoint.getY() < newPoint.getY()) {
                    //da controllare se non è fuori dai bordi
                    return false;
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return true;
    }

    public static Color getColoreInContrasto(Color c) {
        Color txtColor = Color.WHITE;
        if (c.getGreen() > 190) {
            txtColor = Color.BLACK;
        } else if (c.getRed() > 190) {
            if ((c.getGreen() > 190 || c.getBlue() > 190) || (c.getGreen() > 127 || c.getBlue() > 127)) {
                txtColor = Color.BLACK;
            }
        } else if (c.getBlue() > 190) {
            if ((c.getGreen() > 190 || c.getRed() > 190) || (c.getGreen() > 128 || c.getRed() > 128)) {
                txtColor = Color.BLACK;
            }
        }
        return txtColor;
    }

    public static int getHalfDuration(FixtureDetails fixtureDetails) {
        int durataTempo = 45;
        if (fixtureDetails != null && fixtureDetails.getEndTime1() != null && fixtureDetails.getStartTime1() != null && fixtureDetails.getEndTime1() > -1) {
            long t1 = (fixtureDetails.getEndTime1() - fixtureDetails.getStartTime1()) / 1000 / 60;
            //int minT = t1;
            // 2024/10/14 caso partita interrotta definitivamente, va preso il massimo
            long maxT = t1;
            if (fixtureDetails.getEndTime2() != null && fixtureDetails.getStartTime2() != null && fixtureDetails.getEndTime2() > -1) {
                // se ho secondo tempo allora prendo la durata minore dei 2 tempi per calcolare

                long t2 = (fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2()) / 1000 / 60;
                if (t2 > 5) { // ci sono partite che hanno il secondo tempo di durata 20 secondi, in questo caso non posso prendere quello per calcolare
                    //minT = Math.min(t1, t2);
                    maxT = Math.max(t1, t2);
                }
            }
            if (maxT < 20) {
                durataTempo = 15;
            } else if (maxT < 25) {
                durataTempo = 20;
            } else if (maxT < 30) {
                durataTempo = 25;
            } else if (maxT < 35) {
                durataTempo = 30;
            } else if (maxT < 40) {
                durataTempo = 35;
            } else if (maxT < 45) {
                durataTempo = 40;
            }
        }
        return durataTempo;
    }

    //    public static String saveTemporaryReportImage(BufferedImage image) {
//        return saveTemporaryReportImage(null, image);
//    }
//
//    public static String saveTemporaryReportImage(String fileName, BufferedImage image) {
//        try {
//            String folderPath = kServletContextPathImages + "matchstudio/temporary/";
//
//            File file = new File(folderPath);
//            if (!file.exists()) { // controllo per sicurezza che esista la cartella base
//                file.mkdirs();
//            }
//
//            if (fileName == null) {
//                fileName = UUID.randomUUID().toString() + ".png";
//            } else {
//                fileName += ".png";
//            }
//
//            // TODO: sistemare questa parte qua sotto che guarda il nome del file
//            boolean validFileName = false;
//            do { // uso un do while per controllare che ogni volta abbiamo file diversi
//                file = new File(folderPath + fileName);
//                if (!file.exists()) {
//                    file.createNewFile();
//                    validFileName = true;
//                } else {
//                    fileName = UUID.randomUUID().toString() + ".png";
//                }
//            } while (!validFileName);
//            ImageIO.write(image, "png", file);
//            return file.getAbsolutePath();
//        } catch (IOException ex) {
//            GlobalHelper.reportError(ex);
//        }
//
//        return "";
//    }
    public static String drawFieldTiriByLists(List<Event> golList, List<Event> inPortaList, List<Event> fuoriPortaList) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 15;

            for (Event event : inPortaList) {
                double posX = -1;
                double posY = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                }
            }
            for (Event event : fuoriPortaList) {
                double posX = -1;
                double posY = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaCroce(field, p.getX(), p.getY(), ellipseSize - 2);
                }
            }
            for (Event event : golList) {
                double posX = -1;
                double posY = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallone(field, p.getX(), p.getY(), ellipseSize + 5);
                }
            }

            // ruoto di 180 gradi per avere sopra il terzo difensivo
            field = rotateImageBy(field, 180);
            // ora taglio alla metà del campo
            field = field.getSubimage(0, 0, moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * (EventHelper.kLatoLungoCampo / 3));
            field = rotateImageBy(field, 180);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldTiriPortaByLists(List<Event> golList, List<Event> inPortaList, List<Event> fuoriPortaList) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = new BufferedImage(kPorta2023.getWidth(), kPorta2023.getHeight(), kPorta2023.getType());
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.drawImage(kPorta2023, 0, 0, null);
            //diametro dei pallini
            int ellipseSize = 70;

            for (Event event : inPortaList) {
                double posX = -1;
                double posY = -1;
                if (!event.getHeightPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getHeightPointNormalized().getY();
                    posY = moltFieldBig * event.getHeightPointNormalized().getZ();
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = new Point(posX, posY);//scalaSuFieldBig(posX, posY);
                    p = getPuntoScalatoPorta2023(p);
                    //centra pallini tagliati vicino ai bordi del campo
//                    centraPalliniTagliati(field, p, ellipseSize);
                    centraPuntoTagliatoPorta2023(p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                }
            }
            for (Event event : fuoriPortaList) {
                double posX = -1;
                double posY = -1;
                if (!event.getHeightPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getHeightPointNormalized().getY();
                    posY = moltFieldBig * event.getHeightPointNormalized().getZ();
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = new Point(posX, posY);//scalaSuFieldBig(posX, posY);
                    p = getPuntoScalatoPorta2023(p);
                    //centra pallini tagliati vicino ai bordi del campo
//                    centraPalliniTagliati(field, p, ellipseSize);
                    centraPuntoTagliatoPorta2023(p, Math.round(ellipseSize * 2 / 3));
                    disegnaCroce(field, p.getX(), p.getY(), Math.round(ellipseSize * 2 / 3));
                }
            }
            for (Event event : golList) {
                double posX = -1;
                double posY = -1;
                if (!event.getHeightPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getHeightPointNormalized().getY();
                    posY = moltFieldBig * event.getHeightPointNormalized().getZ();
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = new Point(posX, posY);//scalaSuFieldBig(posX, posY);
                    p = getPuntoScalatoPorta2023(p);
                    //centra pallini tagliati vicino ai bordi del campo
//                    centraPalliniTagliati(field, p, ellipseSize);
                    centraPuntoTagliatoPorta2023(p, ellipseSize);
                    disegnaPallone(field, p.getX(), p.getY(), ellipseSize);
                }
            }

        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldWithArrowByActionList(List<Event> orangeActionList, List<Event> blueActionList, String numberToExclude, Map<Long, FixturePlayer> fixturePlayersMap) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 12;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();
            for (Event event : orangeActionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    if (!event.getPlayerIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerIds().get(0))) {
                        String numberToAdd = fixturePlayersMap.get(event.getPlayerIds().get(0)).getJerseyNumber().toString();
                        if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                            puntiOccupatiPallini.add(p);
                            numeri.add(numberToAdd);
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                        if (!event.getPlayerToIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerToIds().get(0))) {
                            String numberToAdd = fixturePlayersMap.get(event.getPlayerToIds().get(0)).getJerseyNumber().toString();
                            if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                                numeri.add(numberToAdd);
                                puntiOccupatiPallini.add(pEnd);
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.YELLOW);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (Event event : blueActionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.BLUE);
                    if (!event.getPlayerIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerIds().get(0))) {
                        String numberToAdd = fixturePlayersMap.get(event.getPlayerIds().get(0)).getJerseyNumber().toString();
                        if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                            puntiOccupatiPallini.add(p);
                            numeri.add(numberToAdd);
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.BLUE);
                        if (!event.getPlayerToIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerToIds().get(0))) {
                            String numberToAdd = fixturePlayersMap.get(event.getPlayerToIds().get(0)).getJerseyNumber().toString();
                            if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                                numeri.add(numberToAdd);
                                puntiOccupatiPallini.add(pEnd);
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.BLUE);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSizeBig);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldWithArrowByActionList(List<Event> orangeActionList, List<Event> blueActionList, List<Event> cyanActionList, String numberToExclude, Map<Long, FixturePlayer> fixturePlayersMap) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 12;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();

            for (Event event : orangeActionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    if (!event.getPlayerIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerIds().get(0))) {
                        String numberToAdd = fixturePlayersMap.get(event.getPlayerIds().get(0)).getJerseyNumber().toString();
                        if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                            puntiOccupatiPallini.add(p);
                            numeri.add(numberToAdd);
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                        if (!event.getPlayerToIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerToIds().get(0))) {
                            String numberToAdd = fixturePlayersMap.get(event.getPlayerToIds().get(0)).getJerseyNumber().toString();
                            if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                                numeri.add(numberToAdd);
                                puntiOccupatiPallini.add(pEnd);
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.YELLOW);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (Event event : blueActionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.BLUE);
                    if (!event.getPlayerIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerIds().get(0))) {
                        String numberToAdd = fixturePlayersMap.get(event.getPlayerIds().get(0)).getJerseyNumber().toString();
                        if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                            puntiOccupatiPallini.add(p);
                            numeri.add(numberToAdd);
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.BLUE);
                        if (!event.getPlayerToIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerToIds().get(0))) {
                            String numberToAdd = fixturePlayersMap.get(event.getPlayerToIds().get(0)).getJerseyNumber().toString();
                            if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                                numeri.add(numberToAdd);
                                puntiOccupatiPallini.add(pEnd);
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.BLUE);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (Event event : cyanActionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.CYAN);
                    if (!event.getPlayerIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerIds().get(0))) {
                        String numberToAdd = fixturePlayersMap.get(event.getPlayerIds().get(0)).getJerseyNumber().toString();
                        if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                            puntiOccupatiPallini.add(p);
                            numeri.add(numberToAdd);
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.CYAN);
                        if (!event.getPlayerToIds().isEmpty() && fixturePlayersMap.containsKey(event.getPlayerToIds().get(0))) {
                            String numberToAdd = fixturePlayersMap.get(event.getPlayerToIds().get(0)).getJerseyNumber().toString();
                            if (!StringUtils.equalsIgnoreCase(numberToAdd, numberToExclude)) {
                                numeri.add(numberToAdd);
                                puntiOccupatiPallini.add(pEnd);
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.CYAN);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSizeBig);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldPalleLateraliByList(List<Event> actionList) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 15;

            for (Event event : actionList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.YELLOW);
                    //metodo che disegna le frecce
                    adjustLineCoords(p, pEnd, ellipseSize);
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }

            // ruoto di 180 gradi per avere sopra il terzo difensivo
            field = rotateImageBy(field, 180);
            // ora taglio alla metà del campo
            field = field.getSubimage(0, 0, moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * (EventHelper.kLatoLungoCampo / 3));
            field = rotateImageBy(field, 180);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldFormazioniVerticale(List<String> top3Position, Long teamId, String module, Map<Long, FixturePlayer> fixturePlayersMap) {
        BufferedImage field = null;
        try {
            int wField = moltCampo * EventHelper.kLatoLungoCampo;
            int hField = moltCampo * EventHelper.kLatoCortoCampo;
            int dimEllipse = 35;

            field = drawEmptyHorizontalField(wField, hField);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            Font font = new Font("Roboto", Font.BOLD, 25);
            AffineTransform affineTransform = new AffineTransform();
            // affineTransform.rotate(Math.toRadians(nTeam == 1 ? 90 : 270), 0, 0);
            affineTransform.rotate(Math.toRadians(90), 0, 0);
            Font rotatedFont = font.deriveFont(affineTransform);
            fieldGraphics.setFont(rotatedFont);

            List<FixturePlayer> teamPlayers = new ArrayList<>();
            for (FixturePlayer fixturePlayer : fixturePlayersMap.values()) {
                if (fixturePlayer.getTeamId() != null && Long.compare(fixturePlayer.getTeamId(), teamId) == 0) {
                    teamPlayers.add(fixturePlayer);
                }
            }

            Point portierePoint = null;
            for (FixturePlayer fixturePlayer : teamPlayers) {
                if (fixturePlayer.getPositionId() != null && fixturePlayer.getPositionId() == 1) {
                    portierePoint = ModuleHelper.getPosPlayerByModuleReport(module, fixturePlayer.getModulePosition() - 1);
                    portierePoint.setX(portierePoint.getX() / 10 * 105);
                    portierePoint.setY(portierePoint.getY() / 8 * 68);
                    portierePoint.setX(moltCampo * portierePoint.getX());
                    portierePoint.setY(moltCampo * portierePoint.getY());
                    portierePoint = scalaSuFieldBig(portierePoint.getX(), portierePoint.getY());
                }
            }

            // disegno le righe per i top 3
            for (FixturePlayer fixturePlayer : teamPlayers) {
                Point point = ModuleHelper.getPosPlayerByModuleReport(module, fixturePlayer.getModulePosition() - 1);
                point.setX(point.getX() / 10 * 105);
                point.setY(point.getY() / 8 * 68);
                point.setX(moltCampo * point.getX());
                point.setY(moltCampo * point.getY());
                point = scalaSuFieldBig(point.getX(), point.getY());
                if (fixturePlayer.getModulePosition() <= 11 && BooleanUtils.isFalse(point.getIsDefault())) {
                    // se serve faccio la linea dal portiere al giocatore
                    // da lasciare qua altrimenti sovrascrive il pallino e i numero
                    if (fixturePlayer.getJerseyNumber() != null) {
                        if (top3Position.contains(fixturePlayer.getJerseyNumber().toString()) && portierePoint != null) {
                            //disegno le frecce
                            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                            int size = 3;
                            if (top3Position.indexOf(fixturePlayer.getJerseyNumber().toString()) == 0) {
                                size = 7;
                            } else if (top3Position.indexOf(fixturePlayer.getJerseyNumber().toString()) == 1) {
                                size = 5;
                            }
                            fieldGraphics.setStroke(new BasicStroke(size));
                            fieldGraphics.setPaint(Color.YELLOW);
                            fieldGraphics.drawLine((int) Math.round(portierePoint.getX()), (int) Math.round(portierePoint.getY()), (int) Math.round(point.getX()), (int) Math.round(point.getY()));
                        }
                    }
                }
            }
            // ora disegno i pallini
            for (FixturePlayer fixturePlayer : teamPlayers) {
                Point point = ModuleHelper.getPosPlayerByModuleReport(module, fixturePlayer.getModulePosition() - 1);
                point.setX(point.getX() / 10 * 105);
                point.setY(point.getY() / 8 * 68);
                point.setX(moltCampo * point.getX());
                point.setY(moltCampo * point.getY());
                point = scalaSuFieldBig(point.getX(), point.getY());
                if (fixturePlayer.getModulePosition() <= 11 && BooleanUtils.isFalse(point.getIsDefault())) {
                    disegnaPallini(field, (int) Math.round(point.getX()), (int) Math.round(point.getY()), dimEllipse, Color.ORANGE);
                    fieldGraphics.setPaint(getColoreInContrasto(Color.ORANGE));
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                    //scrivo numero giocatori
                    AttributedString text = new AttributedString(fixturePlayer.getJerseyNumber().toString());
                    text.addAttribute(TextAttribute.FONT, font.deriveFont(Font.BOLD, 25), 0, fixturePlayer.getJerseyNumber().toString().length());

                    float width = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getWidth());
                    float height = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getHeight());
                    //scrivo numero giocatori
                    fieldGraphics.drawString(fixturePlayer.getJerseyNumber().toString(), (int) Math.round(point.getX() - dimEllipse / 2 + height / 5), (int) Math.round(point.getY() - width / 2));
//                    if (nTeam == 1) {
//
//                    } else {
//                        fieldGraphics.drawString(fixturePlayer.getJerseyNumber().toString(), point.getX() + height / 5, point.getY() + width / 2);
//                    }
                }
            }

//            if (nTeam == 2) {
//                field = rotateImageBy(field, 180);
//            }
            BufferedImage verticalField = new BufferedImage(hField, wField, field.getType());
            for (int x = 0; x < wField; x++) {
                for (int y = 0; y < hField; y++) {
                    verticalField.setRGB(y, wField - x - 1, field.getRGB(x, y));
                }
            }
            field = verticalField;
        } catch (Exception e) {
            GlobalHelper.reportError(e);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static MatchStudioHeatMap buildHeatMapFromActionList(List<Event> eventList) {
        MatchStudioHeatMap myMap = null;
        try {
            BufferedImage field = drawEmptyVerticalField(EventHelper.kLatoCortoCampo * moltCampo, EventHelper.kLatoLungoCampo * moltCampo);
            Graphics2D g = field.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            final List<Point> points = new ArrayList<>();

            for (Event event : eventList) {
                Point point = event.getStartPointNormalized();
                if (point != null && point.getX() > 0 && EventHelper.kLatoCortoCampo - Math.round(point.getY()) > 0 && EventHelper.kLatoCortoCampo - Math.round(point.getY()) < EventHelper.kLatoCortoCampo && point.getX() < EventHelper.kLatoLungoCampo) {
                    point = new Point(point.getY() * 4, (EventHelper.kLatoLungoCampo - point.getX()) * 4);
                    points.add(point);
                }
            }
            myMap = new MatchStudioHeatMap(points, field);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return myMap;
    }

    public static String drawFieldPointsByActionList(List<Event> eventList) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalFieldNoCentro(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            drawCanaliLines(field, 5, true);
            drawTerziLines(field, 3);
            //diametro dei pallini
            int ellipseSize = 12;

            for (Event event : eventList) {
                double posX = -1;
                double posY = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    //Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldDuelliDribblingByActionList(List<Event> duelliVintiList, List<Event> duelliList, List<Event> dribblingVintiList, List<Event> dribblingList) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 10;

            for (Event event : duelliVintiList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                    }
                }
            }
            for (Event event : duelliList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.YELLOW);
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.YELLOW);
                    }
                }
            }
            for (Event event : dribblingVintiList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.BLUE);
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.BLUE);
                    }
                }
            }
            for (Event event : dribblingList) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.CYAN);
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.CYAN);
                    }
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldFormazioni(Team homeTeam, List<FixturePlayer> homeTeamPlayers, Team awayTeam, List<FixturePlayer> awayTeamPlayers, String homeModule, String awayModule, Map<Long, FixturePlayer> fixturePlayersMap, Map<Long, Player> playersMap, FixtureDetails fixtureDetails) {
        BufferedImage field = null;
        try {
            int wField = 8 * EventHelper.kLatoLungoCampo;
            int hField = 8 * EventHelper.kLatoCortoCampo;
            int dimEllipse = 35;

            field = drawEmptyHorizontalField(wField, hField);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);

            Color colorHomeTeam = Color.RED;
            if (fixtureDetails.getHomeColor() != null) {
                colorHomeTeam = fixtureDetails.getHomeColor();
            } else {
                if (StringUtils.isNotBlank(homeTeam.getColor())) {
                    String[] colorString = homeTeam.getColor().split("x");
                    if (colorString.length == 4) {
                        colorHomeTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
                    }
                }
            }
            Color colorAwayTeam = Color.BLUE;
            if (fixtureDetails.getAwayColor() != null) {
                colorAwayTeam = fixtureDetails.getAwayColor();
            } else {
                if (StringUtils.isNotBlank(awayTeam.getColor())) {
                    String[] colorString = awayTeam.getColor().split("x");
                    if (colorString.length == 4) {
                        colorAwayTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
                    }
                }
            }

            Font fontone = new Font("Roboto", Font.PLAIN, mFontSizeBig);

            // ModuleHelper.setPositionPlayersByModuleReport(homeTeam, partita.getmModule1());
            List<Rectangle2D> rectanglesNames = new ArrayList<>();
            for (FixturePlayer fixturePlayer : homeTeamPlayers) {
                Player player = playersMap.get(fixturePlayer.getPlayerId());
                Point point = ModuleHelper.getPosPlayerByModuleReport(homeModule, fixturePlayer.getModulePosition() - 1);
                if (fixturePlayer.getModulePosition() <= 11 && point.getX() != -1 && point.getY() != -1) {
                    point.setX(point.getX() * (field.getWidth() / 2) / 10 + field.getWidth() / 15);
                    point.setY(point.getY() * field.getHeight() / 8);

                    disegnaPallini(field, point.getX(), point.getY(), dimEllipse, colorHomeTeam);
                    fieldGraphics.setFont(fontone.deriveFont(Font.BOLD, 25));
                    fieldGraphics.setPaint(colorHomeTeam);
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                    AttributedString text = new AttributedString(fixturePlayer.getJerseyNumber().toString());
                    text.addAttribute(TextAttribute.FONT, fontone.deriveFont(Font.BOLD, 25), 0, fixturePlayer.getJerseyNumber().toString().length());

                    float width = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getWidth());
                    float height = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getHeight());
                    //scrivo numero giocatori
                    fieldGraphics.drawString(fixturePlayer.getJerseyNumber().toString(), (int) (point.getX() - width / 2), (int) (point.getY() + height / 5));
                    //scrivo nomi giocatori
                    fieldGraphics.setPaint(Color.BLACK);
                    fieldGraphics.setFont(fontone.deriveFont(Font.BOLD, 18));

                    text = new AttributedString(formatName(player.getKnownName()));
                    text.addAttribute(TextAttribute.FONT, fontone.deriveFont(Font.BOLD, 18), 0, formatName(player.getKnownName()).length());
                    width = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getWidth());
                    height = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getHeight());
                    Rectangle2D rr = new Rectangle2D.Float((float) (point.getX() - (width / 2)), (float) (point.getY() + (double) dimEllipse / 2 + height), width, height);
                    boolean contained = false;
                    for (Rectangle2D r : rectanglesNames) {
                        if (r.intersects(rr)) {
                            contained = true;
                            break;
                        }
                    }
                    if (contained) {
                        rr = new Rectangle2D.Float((float) (point.getX() - (width / 2)), (float) (point.getY() - height), width, height);
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(formatName(player.getKnownName()), (float) (point.getX() - (width / 2)), (float) (point.getY() - height - height / 4));
                    } else {
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(formatName(player.getKnownName()), (float) (point.getX() - (width / 2)), (float) (point.getY() + (double) dimEllipse / 2 + height));
                    }
                }
            }

            // ModuleHelper.setPositionPlayersByModuleReport(team2, partita.getmModule2());
            for (FixturePlayer fixturePlayer : awayTeamPlayers) {
                Player player = playersMap.get(fixturePlayer.getPlayerId());
                Point point = ModuleHelper.getPosPlayerByModuleReport(awayModule, fixturePlayer.getModulePosition() - 1);
                if (fixturePlayer.getModulePosition() <= 11 && point.getX() != -1 && point.getY() != -1) {
                    point.setX((10 - point.getX()) * (field.getWidth() / 2) / 10 + field.getWidth() / 2 - field.getWidth() / 15);
                    point.setY((8 - point.getY()) * field.getHeight() / 8);

                    disegnaPallini(field, point.getX(), point.getY(), dimEllipse, colorAwayTeam);
                    fieldGraphics.setFont(fontone.deriveFont(Font.BOLD, 25));
                    fieldGraphics.setPaint(colorAwayTeam);
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                    AttributedString text = new AttributedString(fixturePlayer.getJerseyNumber().toString());
                    text.addAttribute(TextAttribute.FONT, fontone.deriveFont(Font.BOLD, 25), 0, fixturePlayer.getJerseyNumber().toString().length());

                    float width = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getWidth());
                    float height = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getHeight());
                    //scrivo numero giocatori
                    fieldGraphics.drawString(fixturePlayer.getJerseyNumber().toString(), (int) (point.getX() - width / 2), (int) (point.getY() + height / 5));
                    //scrivo nomi giocatori
                    fieldGraphics.setPaint(Color.BLACK);
                    fieldGraphics.setFont(fontone.deriveFont(Font.BOLD, 18));

                    text = new AttributedString(formatName(player.getKnownName()));
                    text.addAttribute(TextAttribute.FONT, fontone.deriveFont(Font.BOLD, 18), 0, formatName(player.getKnownName()).length());
                    width = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getWidth());
                    height = Math.round(fieldGraphics.getFontMetrics().getStringBounds(text.getIterator(), 0, text.getIterator().getEndIndex(), fieldGraphics).getHeight());
                    Rectangle2D rr = new Rectangle2D.Float((float) (point.getX() - (width / 2)), (float) (point.getY() + (double) dimEllipse / 2 + height), width, height);
                    boolean contained = false;
                    for (Rectangle2D r : rectanglesNames) {
                        if (r.intersects(rr)) {
                            contained = true;
                            break;
                        }
                    }
                    if (contained) {
                        rr = new Rectangle2D.Float((float) (point.getX() - (width / 2)), (float) (point.getY() - height), width, height);
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(formatName(player.getKnownName()), (float) (point.getX() - (width / 2)), (float) (point.getY() - height - height / 4));
                    } else {
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(formatName(player.getKnownName()), (float) (point.getX() - (width / 2)), (float) (point.getY() + (double) dimEllipse / 2 + height));
                    }
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static void drawPallinoPosizioneMedia(BufferedImage field, Player player, FixturePlayer fixturePlayer, Point posizione, int dimEllipse, Color colore, Color coloreTesto, List<Event> sostituzioni, List<Event> subentri, boolean isOrizzontale) {
        try {
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();

            Graphics2D graphics = (Graphics2D) field.createGraphics();
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            if (fixturePlayer.getModulePosition() <= 11) {
                disegnaPallini(field, posizione.getX(), posizione.getY(), dimEllipse, colore);
            } else {
                disegnaPalliniTrasparenti(field, posizione.getX(), posizione.getY(), dimEllipse);
            }
            if (fixturePlayer.getModulePosition() <= 11) {
                graphics.setPaint(coloreTesto);
            } else { // se ho messo il pallino trasparente, scrivo il numero in nero
                graphics.setPaint(Color.BLACK);
            }
            Font font = new Font("TimesRoman", Font.BOLD, 14);
            graphics.setFont(font);
            //scrivo numero giocatori
            if (!sostituzioni.isEmpty() && !subentri.isEmpty()) {
                // OUT AND IN
                if (isOrizzontale) {
                    double rotationRequired = Math.toRadians(-90);
                    double locationX = kIconReportSostituzione.getWidth() / 2D;
                    double locationY = kIconReportSostituzione.getHeight() / 2D;

                    AffineTransform tx = AffineTransform.getRotateInstance(rotationRequired, locationX, locationY);
                    AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
                    graphics.drawImage(op.filter(kIconReportSostituzione, null), (int) Math.round(posizione.getX() - 7), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);

                    rotationRequired = Math.toRadians(-90);
                    locationX = kIconReportSubentro.getWidth() / 2D;
                    locationY = kIconReportSubentro.getHeight() / 2D;

                    tx = AffineTransform.getRotateInstance(rotationRequired, locationX, locationY);
                    op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
                    graphics.drawImage(op.filter(kIconReportSubentro, null), (int) Math.round(posizione.getX() - 7), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                } else {
                    graphics.drawImage(kIconReportSostituzione, (int) Math.round(posizione.getX() - (dimEllipse / 2D)), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                    graphics.drawImage(kIconReportSubentro, (int) Math.round(posizione.getX()), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                }
            } else if (!sostituzioni.isEmpty()) {
                // OUT
                if (isOrizzontale) {
                    double rotationRequired = Math.toRadians(-90);
                    double locationX = kIconReportSostituzione.getWidth() / 2D;
                    double locationY = kIconReportSostituzione.getHeight() / 2D;

                    AffineTransform tx = AffineTransform.getRotateInstance(rotationRequired, locationX, locationY);
                    AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
                    graphics.drawImage(op.filter(kIconReportSostituzione, null), (int) Math.round(posizione.getX() - 7), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                } else {
                    graphics.drawImage(kIconReportSostituzione, (int) Math.round(posizione.getX()), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                }
            } else if (!subentri.isEmpty()) {
                // IN
                if (isOrizzontale) {
                    double rotationRequired = Math.toRadians(-90);
                    double locationX = kIconReportSubentro.getWidth() / 2D;
                    double locationY = kIconReportSubentro.getHeight() / 2D;

                    AffineTransform tx = AffineTransform.getRotateInstance(rotationRequired, locationX, locationY);
                    AffineTransformOp op = new AffineTransformOp(tx, AffineTransformOp.TYPE_BILINEAR);
                    graphics.drawImage(op.filter(kIconReportSubentro, null), (int) Math.round(posizione.getX() - 7), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                } else {
                    graphics.drawImage(kIconReportSubentro, (int) Math.round(posizione.getX()), (int) Math.round(posizione.getY() - (dimEllipse / 2D + dimEllipse / 4D)), 8, 8, null);
                }
            }

            // float height = (float) font.createGlyphVector(metrics.getFontRenderContext(), player.getmNumber()).getVisualBounds().getHeight();
            puntiOccupatiPallini.add(new Point(posizione.getX(), posizione.getY()));
            numeri.add(fixturePlayer.getJerseyNumber().toString());

            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), dimEllipse, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX() + (isOrizzontale ? 5 : 0), puntiOccupatiPallini.get(i).getY() - (isOrizzontale ? 10 : 0), puntiOccupati, puntiOccupatiPallini, mFontSize - 5, (isOrizzontale ? -90 : 0));
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static String drawArrowByPointList(BufferedImage field, Map<Point, Point> pointList) {
        try {
            // tolgo 0.5 e poi 0.75 per evitare che la prima e l'ultima linea siano troppo grande e piccola
            float initialSize = pointList.size() - 0.5F; // usata per la larghezza della freccia
            if (initialSize > 6.25) {
                initialSize = 6.25F;
            }

            for (Map.Entry<Point, Point> row : pointList.entrySet()) {
                initialSize = initialSize - 0.75F;

                // (x - (size / 2), y - (size / 2), size, size)
                // (!) ATTENZIONE: HO USATO UN "TRICK" PER EVITARE CHE LA FRECCIA TOCCHI IL PALLINO (!)
                // IL PALLINO HA UN DIAMETRO DI 10, INVECE QUA LI CONSIDERO COME FOSSERO DI DIAMETRO 20 PER LA FRECCIA
                // E 26 PER LA LINEA COSI' DA EVITARE CHE LA LINEA SUPERI LA FRECCIA
                if (row.getKey() == null) {
                    continue;
                }
                Ellipse2D circle1 = new Ellipse2D.Double(row.getKey().getX() - 10, row.getKey().getY() - 10, 20, 20);
                Ellipse2D circle2 = new Ellipse2D.Double(row.getValue().getX() - 10, row.getValue().getY() - 10, 20, 20);

                Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                fieldGraphics.setPaint(Color.BLACK);
                fieldGraphics.setStroke(new BasicStroke(initialSize + 0.2f));

                // tengo il 20 per calcolare il punto iniziale altrimenti si allontana troppo dall'inizio del pallino
                double from = angleBetween(circle1, circle2);
                circle1 = new Ellipse2D.Double(row.getKey().getX() - 13, row.getKey().getY() - 13, 26, 26);
                circle2 = new Ellipse2D.Double(row.getValue().getX() - 13, row.getValue().getY() - 13, 26, 26);
                // per calcolare invece la fine, aumento la dimensione così da considerare circa circa metà freccia
                double to = angleBetween(circle2, circle1);

                // qua faccio una piccola fix per le frecce che stanno da entrambi i lati
                // faccio partire un pò più avanti la linea così da non sembrare una sbavatura
                circle1 = new Ellipse2D.Double(row.getKey().getX() - 10, row.getKey().getY() - 10, 20, 20);
                Point2D pointFrom = getPointOnCircle(circle1, from);
                Point2D pointTo = getPointOnCircle(circle2, to);
                // torno al 16 per impostare le coordinate della punta della freccia ( si potrebbe portare a 20 per tenere
                // un pò di spazio in più
                circle2 = new Ellipse2D.Double(row.getValue().getX() - 8, row.getValue().getY() - 8, 16, 16);

                Line2D line = new Line2D.Double(pointFrom, pointTo);
                fieldGraphics.draw(line);

                from = angleBetween(circle1, circle2);
                pointTo = getPointOnCircle(circle2, to);

                ArrowHead arrowHead = new ArrowHead(8);
                AffineTransform at = AffineTransform.getTranslateInstance(
                        pointTo.getX() - (arrowHead.getBounds2D().getWidth() / 2d),
                        pointTo.getY());
                at.rotate(from, arrowHead.getBounds2D().getCenterX(), 0);
                arrowHead.transform(at);
                fieldGraphics.setStroke(new BasicStroke(3.5F));
                fieldGraphics.draw(arrowHead);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawField(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        //disegno campo vuoto
        BufferedImage field = null;
        try {
            //field = ImageIO.read(new File(GlobalHelper.getkPathReport() + "campoGrigioArancio.png"));
            field = drawEmptyVerticalField(EventHelper.kLatoCortoCampo * moltCampo, EventHelper.kLatoLungoCampo * moltCampo);

            float sommaAltezze = 0;
            int numeroAzioni = 0;
            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;

            int zonaAlta = 35 * moltCampo;
            int zonaBassa = (EventHelper.kLatoLungoCampo - 35) * moltCampo;

            //disegno settori campo
            Graphics2D g = (Graphics2D) field.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setPaint(Color.BLACK);
            BasicStroke bs = new BasicStroke(1, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 15}, 7);
            g.setStroke(bs);
            g.drawLine(0, zonaAlta, EventHelper.kLatoCortoCampo * moltCampo, zonaAlta);
            g.drawLine(0, zonaBassa, EventHelper.kLatoCortoCampo * moltCampo, zonaBassa);

            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();
            Long eventTypeId = null;
            if (events != null && !events.isEmpty()) {
                eventTypeId = events.get(0).getEventTypeId();
                for (Event event : events) {
                    if (!event.getStartPointNormalized().getIsDefault()) {
                        // posizione float per calcolo altezza media
                        double posX = -1;
                        double posY = -1;
                        posX = moltCampo * event.getStartPointNormalized().getY();
                        posY = moltCampo * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                        Point point = scalaSuFieldBig(posX, posY);
                        //centra pallini tagliati vicino ai bordi del campo
                        centraPalliniTagliati(field, point, ellipseSize);
                        posX = point.getX();
                        posY = point.getY();
                        //se sto disegnando le palle recuperate, non disegno quelle del portiere
                        boolean hasGoalkeeper = false;
                        if (event.getPlayerIds() != null) {
                            for (Long playerId : event.getPlayerIds()) {
                                if (fixturePlayerMap.containsKey(playerId) && fixturePlayerMap.get(playerId).getPositionId() == 1) {
                                    hasGoalkeeper = true;
                                    break;
                                }
                            }
                        }
                        if (event.getEventTypeId() != 11L || !hasGoalkeeper) {
                            disegnaPallini(field, point, ellipseSize, Color.ORANGE);
                            puntiOccupatiPallini.add(new Point(posX, posY));
                            if (event.getPlayerIds() != null) {
                                for (Long playerId : event.getPlayerIds()) {
                                    if (fixturePlayerMap.containsKey(playerId)) {
                                        numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (eventTypeId != null && eventTypeId != 10L && eventTypeId != 11L) {
                //disegno i numeri sul campo cercando di non farli sovrapporre ad altri numeri o pallini
                for (int i = 0; i < numeri.size(); i++) {
                    if (!numeri.get(i).isEmpty()) {
                        puntiOccupati = drawNumber(field, moltCampo, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                    }
                }
            }
            //disegno la linea della media
            drawMediaLine(field, moltCampo, sommaAltezze, numeroAzioni);
        } catch (Exception e) {
            GlobalHelper.reportError(e);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldDuelli(List<Event> events) {
        //disegno campo vuoto
        BufferedImage field = null;
        try {
            field = drawEmptyVerticalField(EventHelper.kLatoCortoCampo * moltCampo, EventHelper.kLatoLungoCampo * moltCampo);
            //diametro dei pallini
            int ellipseSize = 10;
            int zonaAlta = 35 * moltCampo;
            int zonaBassa = (EventHelper.kLatoLungoCampo - 35) * moltCampo;

            //disegno settori campo
            Graphics2D g = (Graphics2D) field.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setPaint(Color.BLACK);
            BasicStroke bs = new BasicStroke(1, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 15}, 7);
            g.setStroke(bs);
            g.drawLine(0, zonaAlta, EventHelper.kLatoCortoCampo * moltCampo, zonaAlta);
            g.drawLine(0, zonaBassa, EventHelper.kLatoCortoCampo * moltCampo, zonaBassa);

//            g.drawLine(fasciaSx, 0, fasciaSx, GlobalHelper.kLatoLungoCampo * moltCampo);
//            g.drawLine(fasciaDx, 0, fasciaDx, GlobalHelper.kLatoLungoCampo * moltCampo);
            //lista dei punti in cui sono posizionati dei numeri
            if (events != null && !events.isEmpty()) {
                for (Event event : events) {
                    double posX = -1;
                    double posY = -1;
                    posX = moltCampo * event.getStartPointNormalized().getY();
                    posY = moltCampo * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());

                    //disegno i pallini e i numeri sui campi
                    if (posX >= 0 && posY >= 0) {
                        Point point = scalaSuFieldBig(posX, posY);
                        //centra pallini tagliati vicino ai bordi del campo
                        centraPalliniTagliati(field, point, ellipseSize);
                        posX = point.getX();
                        posY = point.getY();
                        if (event.getEventTypeId() == 9L) {
                            disegnaPallini(field, posX, posY, ellipseSize, Color.ORANGE);
                        } else if (event.getEventTypeId() == 23L) {
                            disegnaPallini(field, posX, posY, ellipseSize, Color.RED);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldDribbling(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        //disegno campo vuoto
        BufferedImage field = null;
        try {
            field = drawEmptyVerticalField(EventHelper.kLatoCortoCampo * moltCampo, EventHelper.kLatoLungoCampo * moltCampo);

            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            int zonaAlta = 35 * moltCampo;
            int zonaBassa = (EventHelper.kLatoLungoCampo - 35) * moltCampo;

            //disegno settori campo
            Graphics2D g = (Graphics2D) field.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setPaint(Color.BLACK);
            BasicStroke bs = new BasicStroke(1, BasicStroke.CAP_SQUARE, BasicStroke.JOIN_ROUND, 10, new float[]{5, 15}, 7);
            g.setStroke(bs);
            g.drawLine(0, zonaAlta, EventHelper.kLatoCortoCampo * moltCampo, zonaAlta);
            g.drawLine(0, zonaBassa, EventHelper.kLatoCortoCampo * moltCampo, zonaBassa);

            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();
            for (Event event : events) {
                double posX = -1;
                double posY = -1;
                posX = moltCampo * event.getStartPointNormalized().getY();
                posY = moltCampo * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());

                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point point = scalaSuFieldBig(posX, posY);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, point, ellipseSize);
                    posX = point.getX();
                    posY = point.getY();
                    if (event.getEventTypeId() == 8L) {
                        if (event.getTagTypeIds() == null || !event.getTagTypeIds().contains(5471L)) {
                            disegnaPallini(field, posX, posY, ellipseSize, Color.ORANGE);
                        } else if (event.getTagTypeIds().contains(5471L)) {
                            disegnaPallini(field, posX, posY, ellipseSize, Color.RED);
                        }
                    }

                    puntiOccupatiPallini.add(point);
                    if (event.getPlayerIds() != null) {
                        for (Long playerId : event.getPlayerIds()) {
                            if (fixturePlayerMap.containsKey(playerId)) {
                                numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                            }
                        }
                    }
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltCampo, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldPassaggiChiave(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();

            for (Event event : events) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    puntiOccupatiPallini.add(p);
                    if (event.getPlayerIds() != null) {
                        for (Long playerId : event.getPlayerIds()) {
                            if (fixturePlayerMap.containsKey(playerId)) {
                                numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                            }
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                        puntiOccupatiPallini.add(pEnd);
                        if (event.getPlayerToIds() != null) {
                            for (Long playerId : event.getPlayerToIds()) {
                                if (fixturePlayerMap.containsKey(playerId)) {
                                    numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                                }
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(75L)) {
                        fieldGraphics.setPaint(Color.BLUE);
                    } else {
                        fieldGraphics.setPaint(Color.YELLOW);
                    }
                    //metodo che disegna le frecce
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldPalleLaterali(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        BufferedImage field = null;
        try {

            //disegno mezzo campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            field = field.getSubimage(0, 0, EventHelper.kLatoCortoCampo * moltFieldBig, (EventHelper.kLatoLungoCampo * moltFieldBig) / 2 + 10);
            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();

            for (Event event : events) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }

                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    if (!event.getEndPointNormalized().getIsDefault()) {
                        disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    } else {
                        disegnaPallini(field, p, ellipseSize, Color.RED);
                    }

                    puntiOccupatiPallini.add(p);
                    if (event.getPlayerIds() != null) {
                        for (Long playerId : event.getPlayerIds()) {
                            if (fixturePlayerMap.containsKey(playerId)) {
                                numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                            }
                        }
                    }
                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                        puntiOccupatiPallini.add(pEnd);

                        if (event.getPlayerToIds() != null) {
                            for (Long playerId : event.getPlayerToIds()) {
                                if (fixturePlayerMap.containsKey(playerId)) {
                                    numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                                }
                            }
                        }
                    }

                    //disegno le frecce
                    if (!event.getEndPointNormalized().getIsDefault()) {
                        Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                        fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                        fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                        if (event.getPlayerToIds() != null && !event.getPlayerToIds().isEmpty()) {
                            fieldGraphics.setStroke(new BasicStroke(2));
                            fieldGraphics.setPaint(new Color(33, 194, 252));
                        } else {
                            fieldGraphics.setStroke(new BasicStroke(2));
                            fieldGraphics.setPaint(Color.RED);
                        }
                        //metodo che disegna le frecce
                        fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                        fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                    }
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                }
            }

        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    private static BufferedImage drawTimeline(FixtureDetails fixtureDetails, int imgHeight, int timelineXStart, int pallinoSize, int timelineHeight, int moltiplicatore, Locale locale) {
        BufferedImage image = null;
        try {
            //millisecondo in cui inizia il primoTempo
            long startFirstTime = 1;
            if (fixtureDetails.getStartTime1() != null && fixtureDetails.getStartTime1() != -1) {
                startFirstTime = fixtureDetails.getStartTime1();
            }
            //minuto in cui inizia il secondo tempo
            long startSecondTime = 1;
            if (fixtureDetails.getStartTime2() != null && fixtureDetails.getStartTime2() != -1) {
                startSecondTime = ((fixtureDetails.getStartTime2() - startFirstTime) / 1000 / 60);
            }
            //minuto in cui inizia l'eventuale 1 tempo supplementare
            long startFirstTimeSuppl = 1;
            if (fixtureDetails.getStartTime3() != null && fixtureDetails.getStartTime3() != -1) {
                startFirstTimeSuppl = ((fixtureDetails.getStartTime3() - startFirstTime) / 1000 / 60);
            }
            long startSecondTimeSuppl = 1;
            if (fixtureDetails.getStartTime4() != null && fixtureDetails.getStartTime4() != -1) {
                startSecondTimeSuppl = ((fixtureDetails.getStartTime4() - startFirstTime) / 1000 / 60);
            }

            //lunghezza in minuti della partita
            int minutiPartita = Math.toIntExact(fixtureDetails.getMatchMinutes());
            //larghezza della timeline
            int timelineWidth = (minutiPartita + 1) * pallinoSize;
            //larghezza dell'immagine
            int imgWidth = timelineWidth + (2 * timelineXStart);
            int posInizio2t = Math.toIntExact(timelineXStart + (startSecondTime * pallinoSize));
            int posInizio1tSuppl = Math.toIntExact(timelineXStart + (startFirstTimeSuppl * pallinoSize));
            int posInizio2tSuppl = Math.toIntExact(timelineXStart + (startSecondTimeSuppl * pallinoSize));
            //coordinate x per inserire le stanghette coi minuti nel primo tempo
            int pos15 = timelineXStart + (15 * pallinoSize);
            int pos30 = timelineXStart + (30 * pallinoSize);
            int pos45 = timelineXStart + (45 * pallinoSize);
            //coordinate x per inserire le stanghette coi minuti nel secondo tempo
            int pos15st = posInizio2t + (15 * pallinoSize);
            int pos30st = posInizio2t + (30 * pallinoSize);
            int pos45st = posInizio2t + (45 * pallinoSize);
            //creo l'immagine
            image = new BufferedImage(imgWidth, imgHeight, BufferedImage.TYPE_INT_ARGB);
            //disegno la timeline dell'intera partita
            Graphics2D graphics = (Graphics2D) image.createGraphics();
            graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            Rectangle timeline = new Rectangle(timelineXStart, imgHeight - timelineHeight - (15 * moltiplicatore), timelineWidth, timelineHeight);
            graphics.setPaint(Color.LIGHT_GRAY);
            graphics.draw(timeline);
            graphics.fill(timeline);

            //disegno la linea divisoria dei tempi
            graphics.setStroke(new BasicStroke(8));
            graphics.setPaint(Color.GRAY);
            graphics.drawLine(timelineXStart, 0, timelineXStart + timelineWidth, 0);
            graphics.drawLine(timelineXStart, 0, timelineXStart, imgHeight - 15 * moltiplicatore);
            graphics.drawLine(timelineXStart + timelineWidth, 0, timelineXStart + timelineWidth, imgHeight - (15 * moltiplicatore));
            graphics.drawLine(posInizio2t, 0, posInizio2t, imgHeight - (15 * moltiplicatore));
            if (startFirstTimeSuppl != 1) {
                graphics.drawLine(posInizio1tSuppl, 0, posInizio1tSuppl, imgHeight - (15 * moltiplicatore));
            }
            if (startSecondTimeSuppl != 1) {
                graphics.drawLine(posInizio2tSuppl, 0, posInizio2tSuppl, imgHeight - (15 * moltiplicatore));
            }
            graphics.setPaint(Color.BLACK);
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            Font font = new Font("TimesRoman", Font.PLAIN, 14);
            graphics.setFont(font.deriveFont(Font.PLAIN, 11 * moltiplicatore));
            graphics.drawString(SpringApplicationContextHelper.getMessage("match.studio.primo.tempo", locale), timelineXStart + (posInizio2t - timelineXStart) / 2 - (20 * moltiplicatore), (0 + 12) * moltiplicatore);
            graphics.drawString(SpringApplicationContextHelper.getMessage("match.studio.secondo.tempo", locale), (posInizio2t + (posInizio2t - posInizio1tSuppl) / 2) - (20 * moltiplicatore), (0 + 12) * moltiplicatore);
            if (startFirstTimeSuppl != 1) {
                graphics.drawString(SpringApplicationContextHelper.getMessage("match.studio.primo.tempo", locale) + " Sup.", (posInizio1tSuppl + (posInizio2tSuppl - posInizio1tSuppl) / 2) - (20 * moltiplicatore), (0 + 12) * moltiplicatore);
            }
            if (startSecondTimeSuppl != 1) {
                graphics.drawString(SpringApplicationContextHelper.getMessage("match.studio.secondo.tempo", locale) + " Sup.", (posInizio2tSuppl + (timelineWidth - posInizio2tSuppl) / 2) - (20 * moltiplicatore), (0 + 12) * moltiplicatore);
            }
            //disegna linee dei minuti (primo tempo)
            graphics.setPaint(Color.GRAY);
            graphics.drawLine(timelineXStart, imgHeight - timelineHeight - (15 * moltiplicatore) - 3 * moltiplicatore, timelineXStart, imgHeight - (15 * moltiplicatore));
            graphics.setPaint(Color.BLACK);
            graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            graphics.setFont(font.deriveFont(Font.BOLD, 9 * moltiplicatore));
            graphics.drawString("0", timelineXStart - 2 * moltiplicatore, imgHeight - 15 * moltiplicatore + 10 * moltiplicatore);

            if (fixtureDetails.getStartTime2() != null && (fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2() - startFirstTime) / 1000 / 60 > 14) {
                graphics.setPaint(Color.BLACK);
                graphics.drawString("15", pos15 - 5 * moltiplicatore, imgHeight - 15 * moltiplicatore + 10 * moltiplicatore);
                graphics.setPaint(Color.GRAY);
                graphics.drawLine(pos15, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos15, imgHeight - (15 * moltiplicatore));
            }
            if (fixtureDetails.getStartTime2() != null && (fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2() - startFirstTime) / 1000 / 60 > 29) {
                graphics.setPaint(Color.BLACK);
                graphics.drawString("30", pos30 - 5 * moltiplicatore, imgHeight - 15 * moltiplicatore + 10 * moltiplicatore);
                graphics.setPaint(Color.GRAY);
                graphics.drawLine(pos30, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos30, imgHeight - (15 * moltiplicatore));
            }
            if (fixtureDetails.getStartTime2() != null && (fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2() - startFirstTime) / 1000 / 60 > 44) {
                graphics.setPaint(Color.BLACK);
                graphics.drawString("45", pos45 - 5 * moltiplicatore, imgHeight - 15 * moltiplicatore + 10 * moltiplicatore);
                graphics.setPaint(Color.GRAY);
                graphics.drawLine(pos45, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos45, imgHeight - (15 * moltiplicatore));
            }
            //disegna linee dei minuti (secondo tempo)
            if (minutiPartita >= 90) {
                graphics.setPaint(Color.GRAY);
                graphics.drawLine(posInizio2t, imgHeight - timelineHeight - (15 * moltiplicatore) - 3 * moltiplicatore, posInizio2t, imgHeight - (15 * moltiplicatore));
                graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                graphics.setFont(font.deriveFont(Font.BOLD, 9 * moltiplicatore));
                //graphics.drawString("0",posInizio2t, imgHeight-15+10); // non metto lo zero del 2t perchè potrebbe accavallarsi coi 45 del pt
                if (fixtureDetails.getStartTime2() != null && (((fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2()) / 1000 / 60) - startFirstTimeSuppl) > 14) {
                    graphics.setPaint(Color.BLACK);
                    graphics.drawString("15", pos15st - 5 * moltiplicatore, imgHeight - (15 * moltiplicatore) + 10 * moltiplicatore);
                    graphics.setPaint(Color.GRAY);
                    graphics.drawLine(pos15st, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos15st, imgHeight - (15 * moltiplicatore));
                }
                if (fixtureDetails.getStartTime2() != null && (((fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2()) / 1000 / 60) - startFirstTimeSuppl) > 29) {
                    graphics.setPaint(Color.BLACK);
                    graphics.drawString("30", pos30st - 5 * moltiplicatore, imgHeight - (15 * moltiplicatore) + 10 * moltiplicatore);
                    graphics.setPaint(Color.GRAY);
                    graphics.drawLine(pos30st, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos30st, imgHeight - (15 * moltiplicatore));
                }
                if (fixtureDetails.getStartTime2() != null && (((fixtureDetails.getEndTime2() - fixtureDetails.getStartTime2()) / 1000 / 60) - startFirstTimeSuppl) > 44) {
                    graphics.setPaint(Color.BLACK);
                    graphics.drawString("45", pos45st - 5 * moltiplicatore, imgHeight - (15 * moltiplicatore) + 10 * moltiplicatore);
                    graphics.setPaint(Color.GRAY);
                    graphics.drawLine(pos45st, imgHeight - timelineHeight - (15 * moltiplicatore) - 5 * moltiplicatore, pos45st, imgHeight - (15 * moltiplicatore));
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return image;
    }

    public static String drawPassaggiTimeline(FixtureDetails fixtureDetails, List<Event> passaggiChiave, List<Event> passaggiChiaveAssist, List<Event> palleLaterali, Locale locale) {
        BufferedImage field = null;
        try {
            BufferedImage pallinoGiallo;
            BufferedImage pallinoBlu;
            BufferedImage pallinoCiano;

            int moltiplicatore = 8;
            //coordinata x dell'inizio della timeline e spazio bianco ai lati dell'immagine
            int timelineXStart = 10 * moltiplicatore;
            //diametro dei pallini
            int pallinoSize = 6 * moltiplicatore;
            //altezza dell'immagine
            int imgHeight = 66 * moltiplicatore;
            //millisecondo in cui inizia il primo tempo della partita
            //int startFirstTime = Integer.parseInt(String.valueOf(partita.getmStarttime1()));
            //lunghezza in minuti della partita

            int minutiPartita = Math.toIntExact(fixtureDetails.getMatchMinutes());
            int timelineHeight = 7 * moltiplicatore;
            //contatore di eventi in cui la chiave è il minuto in cui accade l'evento
            int[] eventi = new int[minutiPartita + 1];
            //disegno la timeline vuota
            field = drawTimeline(fixtureDetails, imgHeight, timelineXStart, pallinoSize, timelineHeight, moltiplicatore, locale);

            try {
                pallinoBlu = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoBlu.png"));
                pallinoGiallo = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoGiallo.png"));
                pallinoCiano = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoCiano.png"));
                ((Graphics2D) pallinoGiallo.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                ((Graphics2D) pallinoBlu.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                ((Graphics2D) pallinoCiano.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                List<Event> passaggioChiaveNoAssist = new ArrayList<>();
                for (Event pc : passaggiChiave) {
                    if (!passaggiChiaveAssist.contains(pc)) {
                        passaggioChiaveNoAssist.add(pc);
                    }
                }

                //riempio la timeline
                for (Event event : passaggioChiaveNoAssist) {
                    int actionTime = (int) ((event.getStartMillis() - fixtureDetails.getStartTime1()) / 1000 / 60);
                    if (actionTime <= minutiPartita && eventi[actionTime] < 7) {
                        int posX = (timelineXStart + actionTime * pallinoSize) + (pallinoSize / 2);
                        int posY = (imgHeight - timelineHeight - 15 * moltiplicatore - (pallinoSize / 2)) - (eventi[actionTime] * pallinoSize);
                        eventi[actionTime]++;
                        disegnaPalliniTimeline(field, posX, posY, pallinoSize, pallinoGiallo);
                    }
                }
                for (Event event : passaggiChiaveAssist) {
                    int actionTime = (int) ((event.getStartMillis() - fixtureDetails.getStartTime1()) / 1000 / 60);
                    if (actionTime <= minutiPartita && eventi[actionTime] < 7) {
                        int posX = (timelineXStart + actionTime * pallinoSize) + (pallinoSize / 2);
                        int posY = (imgHeight - timelineHeight - 15 * moltiplicatore - (pallinoSize / 2)) - (eventi[actionTime] * pallinoSize);
                        eventi[actionTime]++;
                        disegnaPalliniTimeline(field, posX, posY, pallinoSize, pallinoBlu);
                    }
                }
                for (Event event : palleLaterali) {
                    int actionTime = (int) ((event.getStartMillis() - fixtureDetails.getStartTime1()) / 1000 / 60);
                    if (actionTime <= minutiPartita && eventi[actionTime] < 7) {
                        int posX = (timelineXStart + actionTime * pallinoSize) + (pallinoSize / 2);
                        int posY = (imgHeight - timelineHeight - 15 * moltiplicatore - (pallinoSize / 2)) - (eventi[actionTime] * pallinoSize);
                        eventi[actionTime]++;
                        disegnaPalliniTimeline(field, posX, posY, pallinoSize, pallinoCiano);
                    }
                }
            } catch (IOException e) {
                GlobalHelper.reportError(e);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldTiri(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        BufferedImage field = null;
        try {
            //disegno campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            field = field.getSubimage(0, 0, EventHelper.kLatoCortoCampo * moltFieldBig, (EventHelper.kLatoLungoCampo * moltFieldBig) / 2 + 10);
            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();
            for (Event event : events) {
                double posX = -1;
                double posY = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }

                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);
                    //disegnaPallini(field,posX,posY,ellipseSize, Color.ORANGE);
                    puntiOccupatiPallini.add(p);
                    if (event.getPlayerIds() != null) {
                        for (Long playerId : event.getPlayerIds()) {
                            if (fixturePlayerMap.containsKey(playerId)) {
                                numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                            }
                        }
                    }

                    if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(29L)) {
                        disegnaPallini(field, p, ellipseSize, Color.GRAY);
                    } else if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(38L)) {
                        disegnaPallini(field, p, ellipseSize, Color.YELLOW);
                    } else {
                        disegnaPallini(field, p, ellipseSize, Color.RED);
                    }
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawFieldAssist(List<Event> events, Map<Long, FixturePlayer> fixturePlayerMap) {
        BufferedImage field = null;
        try {
            //disegno mezzo campo vuoto
            field = drawEmptyVerticalField(moltFieldBig * EventHelper.kLatoCortoCampo, moltFieldBig * EventHelper.kLatoLungoCampo);
            //diametro dei pallini
            int ellipseSize = 10;
            //dimensioni stimate in pixel delle stringhe
            int stringXDim = 18;
            int stringYDim = 12;
            //lista dei punti in cui sono posizionati dei numeri
            List<Point> puntiOccupati = new ArrayList<>();
            //lista dei punti in cui sono posizionati dei pallini
            List<Point> puntiOccupatiPallini = new ArrayList<>();
            List<String> numeri = new ArrayList<>();

            for (Event event : events) {
                double posX = -1;
                double posY = -1;
                double posXEnd = -1;
                double posYEnd = -1;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = moltFieldBig * event.getStartPointNormalized().getY();
                    posY = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                    posXEnd = moltFieldBig * event.getEndPointNormalized().getY();
                    posYEnd = moltFieldBig * (EventHelper.kLatoLungoCampo - event.getEndPointNormalized().getX());
                }

                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point p = scalaSuFieldBig(posX, posY);
                    Point pEnd = scalaSuFieldBig(posXEnd, posYEnd);
                    //centra pallini tagliati vicino ai bordi del campo
                    centraPalliniTagliati(field, p, ellipseSize);

                    disegnaPallini(field, p, ellipseSize, Color.ORANGE);
                    puntiOccupatiPallini.add(p);
                    if (event.getPlayerIds() != null) {
                        for (Long playerId : event.getPlayerIds()) {
                            if (fixturePlayerMap.containsKey(playerId)) {
                                numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                            }
                        }
                    }

                    if (posXEnd >= 0 && posYEnd >= 0) {
                        disegnaPallini(field, pEnd, ellipseSize, Color.ORANGE);
                        puntiOccupatiPallini.add(pEnd);
                        if (event.getPlayerToIds() != null) {
                            for (Long playerId : event.getPlayerToIds()) {
                                if (fixturePlayerMap.containsKey(playerId)) {
                                    numeri.add(fixturePlayerMap.get(playerId).getJerseyNumber().toString());
                                }
                            }
                        }
                    }
                    //disegno le frecce
                    Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
                    fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    fieldGraphics.setStroke(new BasicStroke(2));
                    fieldGraphics.setPaint(Color.BLUE);
                    //metodo che disegna le frecce
                    fieldGraphics.drawLine((int) Math.round(p.getX()), (int) Math.round(p.getY()), (int) Math.round(pEnd.getX()), (int) Math.round(pEnd.getY()));
                    fieldGraphics.fill(closeFreccia(EventHelper.kLatoCortoCampo * moltFieldBig, EventHelper.kLatoLungoCampo * moltFieldBig, p, pEnd, -3, 0, 6));
                }
            }
            for (int i = 0; i < numeri.size(); i++) {
                if (!numeri.get(i).isEmpty()) {
                    puntiOccupati = drawNumber(field, moltFieldBig, numeri.get(i), ellipseSize, stringXDim, stringYDim, puntiOccupatiPallini.get(i).getX(), puntiOccupatiPallini.get(i).getY(), puntiOccupati, puntiOccupatiPallini, mFontSize);
                }
            }

            field = field.getSubimage(0, 0, EventHelper.kLatoCortoCampo * moltFieldBig, (EventHelper.kLatoLungoCampo * moltFieldBig) / 2 + 10);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    public static String drawTiriTimeline(FixtureDetails fixtureDetails, List<Event> shots, Locale locale) {
        BufferedImage field = null;
        try {

            BufferedImage pallinoGiallo;
            BufferedImage pallinoRosso, pallinoGrigio;

            //coordinata x dell'inizio della timeline e spazio bianco ai lati dell'immagine
            int moltiplicatore = 8;
            int timelineXStart = 10 * moltiplicatore;
            //diametro dei pallini
            int pallinoSize = 6 * moltiplicatore;
            //altezza dell'immagine
            int imgHeight = 66 * moltiplicatore;
            //millisecondo in cui inizia il primo tempo della partita
            //int startFirstTime = Integer.parseInt(String.valueOf(partita.getmStarttime1()));
            //lunghezza in minuti della partita
            int minutiPartita = Math.toIntExact(fixtureDetails.getMatchMinutes());
            int timelineHeight = 7 * moltiplicatore;
            //contatore di eventi in cui la chiave è il minuto in cui accade l'evento
            int[] eventi = new int[minutiPartita + 1];
            //disegno la timeline vuota
            field = drawTimeline(fixtureDetails, imgHeight, timelineXStart, pallinoSize, timelineHeight, moltiplicatore, locale);

            try {
                pallinoGiallo = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoGiallo.png"));
                pallinoRosso = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoRosso.png"));
                pallinoGrigio = ImageIO.read(new File(kServletContextPathImages + "matchstudio/pallinoGrigio.png"));
                ((Graphics2D) pallinoGiallo.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                ((Graphics2D) pallinoRosso.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                ((Graphics2D) pallinoGrigio.getGraphics()).setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                List<Event> tiriInPorta = new ArrayList<>();
                List<Event> tiriNonInPorta = new ArrayList<>();
                for (Event event : shots) {
                    if (event.getTagTypeIds() != null) {
                        if (event.getTagTypeIds().contains(38L)) {
                            tiriInPorta.add(event);
                        } else if (event.getTagTypeIds().contains(39L)) {
                            tiriNonInPorta.add(event);
                        }
                    }
                }
                //riempio la timeline
                for (Event event : tiriNonInPorta) {
                    int actionTime = (int) ((event.getStartMillis() - fixtureDetails.getStartTime1()) / 1000 / 60);
                    if (actionTime <= minutiPartita && eventi[actionTime] < 7) {
                        int posX = (timelineXStart + actionTime * pallinoSize) + (pallinoSize / 2);
                        int posY = (imgHeight - timelineHeight - 15 * moltiplicatore - (pallinoSize / 2)) - (eventi[actionTime] * pallinoSize);
                        eventi[actionTime]++;
                        disegnaPalliniTimeline(field, posX, posY, pallinoSize, event.getTagTypeIds() != null && event.getTagTypeIds().contains(29L) ? pallinoGrigio : pallinoRosso);
                    }
                }
                for (Event event : tiriInPorta) {
                    int actionTime = (int) ((event.getStartMillis() - fixtureDetails.getStartTime1()) / 1000 / 60);
                    if (actionTime <= minutiPartita && eventi[actionTime] < 7) {
                        int posX = (timelineXStart + actionTime * pallinoSize) + (pallinoSize / 2);
                        int posY = (imgHeight - timelineHeight - 15 * moltiplicatore - (pallinoSize / 2)) - (eventi[actionTime] * pallinoSize);
                        eventi[actionTime]++;
                        disegnaPalliniTimeline(field, posX, posY, pallinoSize, event.getTagTypeIds() != null && event.getTagTypeIds().contains(29L) ? pallinoGrigio : pallinoGiallo);
                    }
                }
            } catch (IOException e) {
                GlobalHelper.reportError(e);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        if (field != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(field, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                return base64Image;
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        return "";
    }

    private static void adjustLineCoords(Point p, Point pEnd, double radius) {
        // Adjust the radius as before.
        double offset = radius / 2 * 1.2;

        // Calculate the angle (in radians) of the line from p to pEnd.
        double angle = Math.atan2(pEnd.getY() - p.getY(), pEnd.getX() - p.getX());

        // Move p in the direction of the line, and pEnd in the opposite direction.
        p.setX(p.getX() + offset * Math.cos(angle));
        p.setY(p.getY() + offset * Math.sin(angle));
        pEnd.setX(pEnd.getX() - offset * Math.cos(angle));
        pEnd.setY(pEnd.getY() - offset * Math.sin(angle));
    }

    //restituisce la stringa in minuscolo con la prima lettera di ogni parola in maiuscolo (ES: Alessandro Del Piero )
    private static String formatName(String name) {
        StringBuilder correctName = new StringBuilder();
        try {
            if (name != null && !name.equalsIgnoreCase("")) {
                name = name.toLowerCase();
                String[] string = name.split(" ");
                for (String s : string) {
                    correctName.append(String.valueOf(s.charAt(0)).toUpperCase()).append(s.substring(1)).append(" ");
                }
            }
            correctName = new StringBuilder(correctName.toString().trim());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return correctName.toString();
    }
}
