package sics.controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.*;
import sics.domain.matchstudio.*;
import sics.helper.EventHelper;
import sics.helper.GlobalHelper;
import sics.helper.MatchStudioHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.service.UserService;

public class MatchStudioController extends BaseController {

    private final static String pageMatchStudio = "auth/matchStudio.jsp";
    private static final UserService uService = new UserService();

    public static String matchStudio(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request, Long fixtureId, List<Long> playerIds, String language, Boolean isPlayerStudio) {
        if (fixtureId != null) {
            String sessionLanguage = "en";
            if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
            }
            // se arriva da parametro uso sempre quella
            if (StringUtils.isNotBlank(language)) {
                sessionLanguage = language;
            }
            Locale locale = BaseController.getLocaleFromLanguange(sessionLanguage);
            RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);
            model.addAttribute("mLanguage", locale.getLanguage());

            Map<String, Object> params = new HashMap<>();
            params.put("fixtureId", fixtureId);

            List<Event> events = uService.getEventsByParams(params);
            Fixture fixture = uService.getFixtureById(fixtureId);
            FixtureDetails fixtureDetail = uService.getFixtureDetails(fixtureId, true);
            List<FixturePlayer> fixturePlayers = uService.getFixturePlayersByParams(params);
            List<AdvancedMetricValue> advancedMetrics = new ArrayList<>();
            List<AdvancedMetricValue> tmpAdvancedMetrics = uService.getMatchStudioTeamAdvancedMetrics(fixtureId);
            if (tmpAdvancedMetrics != null && !tmpAdvancedMetrics.isEmpty()) {
                advancedMetrics.addAll(tmpAdvancedMetrics);
            }
            tmpAdvancedMetrics = uService.getMatchStudioPlayerAdvancedMetrics(fixtureId);
            if (tmpAdvancedMetrics != null && !tmpAdvancedMetrics.isEmpty()) {
                advancedMetrics.addAll(tmpAdvancedMetrics);
            }
            Map<Long, Player> playersMap = new HashMap<>();

            if (fixture != null && fixtureDetail != null && events != null && !events.isEmpty()) {
                // CARICAMENTO COMPETIZIONE
                Competition competition;
                if (competitions.containsKey(fixture.getCompetitionId())) {
                    competition = competitions.get(fixture.getCompetitionId());
                } else {
                    competition = uService.getCompetition(fixture.getCompetitionId());
                }

                // CARICAMENTO TEAM
                List<Team> fixtureTeams = new ArrayList<>();
                List<Long> teamIdsToLoad = new ArrayList<>();
                if (teams.containsKey(fixture.getHomeTeamId())) {
                    fixtureTeams.add(teams.get(fixture.getHomeTeamId()));
                } else {
                    teamIdsToLoad.add(fixture.getHomeTeamId());
                }
                if (teams.containsKey(fixture.getAwayTeamId())) {
                    fixtureTeams.add(teams.get(fixture.getAwayTeamId()));
                } else {
                    teamIdsToLoad.add(fixture.getAwayTeamId());
                }
                if (!teamIdsToLoad.isEmpty()) {
                    fixtureTeams.addAll(uService.getTeams(teamIdsToLoad));
                }
                Map<Long, Team> fixtureTeamsMap = new HashMap<>();
                for (Team team : fixtureTeams) {
                    fixtureTeamsMap.put(team.getId(), team);
                }

                // CARICAMENTO PLAYER
                if (!playerIds.isEmpty()) {
                    for (Long playerId : playerIds) {
                        Player player;
                        if (players.containsKey(playerId)) {
                            player = players.get(playerId);
                        } else {
                            player = uService.getPlayer(playerId, true);
                        }
                        if (player != null) {
                            playersMap.put(player.getId(), player);
                        }
                    }
                }

                // MAPPING FIXTURE_PLAYER
                Map<Long, FixturePlayer> fixturePlayersMap = new HashMap<>();
                Map<Long, FixturePlayer> fixtureHomeTeamPlayersMap = new HashMap<>();
                Map<Long, FixturePlayer> fixtureAwayTeamPlayersMap = new HashMap<>();
                if (fixturePlayers != null) {
                    for (FixturePlayer fixturePlayer : fixturePlayers) {
                        if (fixturePlayer.getPlayerId() != null) {
                            fixturePlayersMap.putIfAbsent(fixturePlayer.getPlayerId(), fixturePlayer);

                            if (fixturePlayer.getTeamId() != null) {
                                if (fixturePlayer.getTeamId().equals(fixture.getHomeTeamId())) {
                                    fixtureHomeTeamPlayersMap.put(fixturePlayer.getPlayerId(), fixturePlayer);
                                } else if (fixturePlayer.getTeamId().equals(fixture.getAwayTeamId())) {
                                    fixtureAwayTeamPlayersMap.put(fixturePlayer.getPlayerId(), fixturePlayer);
                                }
                            }
                        }
                    }
                }

                List<FixturePlayer> tmpTeamPlayers = new ArrayList<>();
                // LISTA GIOCATORI HOME
                List<FixturePlayer> homeTeamPlayers = new ArrayList<>();
                for (FixturePlayer fixturePlayer : fixtureHomeTeamPlayersMap.values()) {
                    if (fixturePlayer.getModulePosition() != null && fixturePlayer.getModulePosition() <= 11) {
                        homeTeamPlayers.add(fixturePlayer);
                    }
                }
                Collections.sort(homeTeamPlayers, new Comparator<FixturePlayer>() {
                    @Override
                    public int compare(FixturePlayer o1, FixturePlayer o2) {
                        if (o1.getPositionId().equals(o2.getPositionId())) {
                            return o1.getJerseyNumber().compareTo(o2.getJerseyNumber());
                        } else {
                            return o1.getPositionId().compareTo(o2.getPositionId());
                        }
                    }
                });
                // ora aggiungo i subentrati
                for (FixturePlayer fixturePlayer : fixtureHomeTeamPlayersMap.values()) {
                    if (fixturePlayer.getModulePosition() != null && fixturePlayer.getModulePosition() > 11) {
                        tmpTeamPlayers.add(fixturePlayer);
                    }
                }
                Collections.sort(tmpTeamPlayers, new Comparator<FixturePlayer>() {
                    @Override
                    public int compare(FixturePlayer o1, FixturePlayer o2) {
                        if (o1.getFromPeriodId().equals(o2.getFromPeriodId())) {
                            return o1.getFromPeriodMinute().compareTo(o2.getFromPeriodMinute());
                        } else {
                            return o1.getFromPeriodId().compareTo(o2.getFromPeriodId());
                        }
                    }
                });
                homeTeamPlayers.addAll(tmpTeamPlayers);

                // LISTA GIOCATORI AWAY
                List<FixturePlayer> awayTeamPlayers = new ArrayList<>();
                for (FixturePlayer fixturePlayer : fixtureAwayTeamPlayersMap.values()) {
                    if (fixturePlayer.getModulePosition() != null && fixturePlayer.getModulePosition() <= 11) {
                        awayTeamPlayers.add(fixturePlayer);
                    }
                }
                Collections.sort(awayTeamPlayers, new Comparator<FixturePlayer>() {
                    @Override
                    public int compare(FixturePlayer o1, FixturePlayer o2) {
                        if (o1.getPositionId().equals(o2.getPositionId())) {
                            return o1.getJerseyNumber().compareTo(o2.getJerseyNumber());
                        } else {
                            return o1.getPositionId().compareTo(o2.getPositionId());
                        }
                    }
                });
                // ora aggiungo i subentrati
                tmpTeamPlayers = new ArrayList<>();
                for (FixturePlayer fixturePlayer : fixtureAwayTeamPlayersMap.values()) {
                    if (fixturePlayer.getModulePosition() != null && fixturePlayer.getModulePosition() > 11) {
                        tmpTeamPlayers.add(fixturePlayer);
                    }
                }
                Collections.sort(tmpTeamPlayers, new Comparator<FixturePlayer>() {
                    @Override
                    public int compare(FixturePlayer o1, FixturePlayer o2) {
                        if (o1.getFromPeriodId().equals(o2.getFromPeriodId())) {
                            return o1.getFromPeriodMinute().compareTo(o2.getFromPeriodMinute());
                        } else {
                            return o1.getFromPeriodId().compareTo(o2.getFromPeriodId());
                        }
                    }
                });
                awayTeamPlayers.addAll(tmpTeamPlayers);

                model.addAttribute("mHomeTeamPlayers", homeTeamPlayers);
                model.addAttribute("mAwayTeamPlayers", awayTeamPlayers);

                // MAPPING EVENT
                // Team, Player, EventType, TagType
                Map<Long, Map<Long, Map<Long, Map<String, List<Event>>>>> groupedEvents = new HashMap<>();
                for (Event event : events) {
                    EventHelper.setEventDetails(event, fixtureDetail);

                    groupedEvents.putIfAbsent(event.getTeamId(), new HashMap<Long, Map<Long, Map<String, List<Event>>>>());
                    // per avere tutti gli eventi di un team passo null come player
                    groupedEvents.get(event.getTeamId()).putIfAbsent(null, new HashMap<Long, Map<String, List<Event>>>());
                    if (event.getPlayerIds() != null) {
                        for (Long tmpPlayerId : event.getPlayerIds()) {
                            groupedEvents.get(event.getTeamId()).putIfAbsent(tmpPlayerId, new HashMap<Long, Map<String, List<Event>>>());
                            if (event.getEventTypeId() != null) {
                                groupedEvents.get(event.getTeamId()).get(tmpPlayerId).putIfAbsent(event.getEventTypeId(), new HashMap<String, List<Event>>());

                                groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).putIfAbsent(null, new ArrayList<Event>());
                                groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).get(null).add(event);
                                if (StringUtils.contains(event.getTagTypeList(), ",")) {
                                    groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).putIfAbsent(event.getTagTypeList(), new ArrayList<Event>());
                                    groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).get(event.getTagTypeList()).add(event);
                                }
                                if (event.getTagTypeIds() != null) {
                                    for (Long tagTypeId : event.getTagTypeIds()) {
                                        groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).putIfAbsent(tagTypeId.toString(), new ArrayList<Event>());
                                        groupedEvents.get(event.getTeamId()).get(tmpPlayerId).get(event.getEventTypeId()).get(tagTypeId.toString()).add(event);
                                    }
                                }
                            }
                        }
                    }

                    if (event.getEventTypeId() != null) {
                        groupedEvents.get(event.getTeamId()).get(null).putIfAbsent(event.getEventTypeId(), new HashMap<String, List<Event>>());

                        groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).putIfAbsent(null, new ArrayList<Event>());
                        groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).get(null).add(event);
                        if (StringUtils.contains(event.getTagTypeList(), ",")) {
                            groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).putIfAbsent(event.getTagTypeList(), new ArrayList<Event>());
                            groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).get(event.getTagTypeList()).add(event);
                        }
                        if (event.getTagTypeIds() != null) {
                            for (Long tagTypeId : event.getTagTypeIds()) {
                                groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).putIfAbsent(tagTypeId.toString(), new ArrayList<Event>());
                                groupedEvents.get(event.getTeamId()).get(null).get(event.getEventTypeId()).get(tagTypeId.toString()).add(event);
                            }
                        }
                    }

                    groupedEvents.get(event.getTeamId()).get(null).putIfAbsent(null, new HashMap<String, List<Event>>());
                    groupedEvents.get(event.getTeamId()).get(null).get(null).putIfAbsent(null, new ArrayList<Event>());
                    groupedEvents.get(event.getTeamId()).get(null).get(null).get(null).add(event);
                }

                // MAPPING EVENT
                // Team, Player, StatsType
                Map<Long, Map<Long, Map<Long, Double>>> groupedAdvancedMetrics = new HashMap<>();
                for (AdvancedMetricValue advancedMetricValue : advancedMetrics) {
                    if (advancedMetricValue.getTeamId() != null && advancedMetricValue.getStatsTypeId() != null) {
                        groupedAdvancedMetrics.putIfAbsent(advancedMetricValue.getTeamId(), new HashMap<Long, Map<Long, Double>>());
                        groupedAdvancedMetrics.get(advancedMetricValue.getTeamId()).putIfAbsent(advancedMetricValue.getPlayerId(), new HashMap<Long, Double>());
                        groupedAdvancedMetrics.get(advancedMetricValue.getTeamId()).get(advancedMetricValue.getPlayerId()).put(advancedMetricValue.getStatsTypeId(), advancedMetricValue.getValue());
                    }
                }

                // CARICO TUTTI I GIOCATORI
                for (FixturePlayer fixturePlayer : fixturePlayers) {
                    Player tmpPlayer;
                    if (players.containsKey(fixturePlayer.getPlayerId())) {
                        tmpPlayer = players.get(fixturePlayer.getPlayerId());
                    } else if (playersMap.containsKey(fixturePlayer.getPlayerId())) {
                        tmpPlayer = playersMap.get(fixturePlayer.getPlayerId());
                    } else {
                        tmpPlayer = uService.getPlayer(fixturePlayer.getPlayerId(), true);
                    }
                    if (tmpPlayer != null) {
                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                    }
                }

                if (playerIds.isEmpty()) {
                    // tutti i giocatori
                    for (FixturePlayer fixturePlayer : fixturePlayers) {
                        Player tmpPlayer;
                        if (players.containsKey(fixturePlayer.getPlayerId())) {
                            tmpPlayer = players.get(fixturePlayer.getPlayerId());
                        } else if (playersMap.containsKey(fixturePlayer.getPlayerId())) {
                            tmpPlayer = playersMap.get(fixturePlayer.getPlayerId());
                        } else {
                            tmpPlayer = uService.getPlayer(fixturePlayer.getPlayerId(), true);
                        }
                        if (tmpPlayer != null) {
                            playerIds.add(tmpPlayer.getId());
                        }
                    }
                }

                // PAGINE INIZIALI
                if (BooleanUtils.isNotTrue(isPlayerStudio)) {
                    // PRIMA PAGINA
                    String firstPageImage = MatchStudioHelper.drawFieldFormazioni(fixtureTeamsMap.get(fixture.getHomeTeamId()), new ArrayList<>(fixtureHomeTeamPlayersMap.values()), fixtureTeamsMap.get(fixture.getAwayTeamId()), new ArrayList<>(fixtureAwayTeamPlayersMap.values()), fixture.getHomeModule().replace("-", ""), fixture.getAwayModule().replace("-", ""), fixturePlayersMap, playersMap, fixtureDetail);
                    model.addAttribute("mFirstPageImage", firstPageImage);

                    // MAPPING PER IL RANKING DEGLI EVENTI
                    // Team, EventType, TagType, PlayerRanking
                    Map<Long, Map<Long, Map<String, List<PlayerRanking>>>> eventRankingMap = new HashMap<>();
                    // Team, EventType, TagType, Player, Counter
                    Map<Long, Map<Long, Map<String, Map<Long, Integer>>>> eventCounterMap = new HashMap<>();

                    for (Event event : events) {
                        if (event.getPlayerIds() != null) {
                            for (Long tmpPlayerId : event.getPlayerIds()) {
                                if (event.getEventTypeId() != null) {
                                    // SALTO LE SIDE BALLS CON IL TAG CROSS BLOCCATO
                                    if (event.getEventTypeId() == 100L) {
                                        if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(2400L)) {
                                            continue;
                                        }
                                    }

                                    eventCounterMap.putIfAbsent(event.getTeamId(), new HashMap<Long, Map<String, Map<Long, Integer>>>());
                                    eventCounterMap.get(event.getTeamId()).putIfAbsent(event.getEventTypeId(), new HashMap<String, Map<Long, Integer>>());
                                    eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).putIfAbsent(null, new HashMap<Long, Integer>());
                                    eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(null).put(tmpPlayerId, eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(null).getOrDefault(tmpPlayerId, 0) + 1);

                                    if (StringUtils.contains(event.getTagTypeList(), ",")) {
                                        eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).putIfAbsent(event.getTagTypeList(), new HashMap<Long, Integer>());
                                        eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(event.getTagTypeList()).put(tmpPlayerId, eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(event.getTagTypeList()).getOrDefault(tmpPlayerId, 0) + 1);
                                    }
                                    if (event.getTagTypeIds() != null) {
                                        for (Long tagTypeId : event.getTagTypeIds()) {
                                            eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).putIfAbsent(tagTypeId.toString(), new HashMap<Long, Integer>());
                                            eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(tagTypeId.toString()).put(tmpPlayerId, eventCounterMap.get(event.getTeamId()).get(event.getEventTypeId()).get(tagTypeId.toString()).getOrDefault(tmpPlayerId, 0) + 1);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    for (Long teamId : eventCounterMap.keySet()) {
                        for (Long eventType : eventCounterMap.get(teamId).keySet()) {
                            for (String tagType : eventCounterMap.get(teamId).get(eventType).keySet()) {
                                // sort by the counter (value of the map)
                                List<Map.Entry<Long, Integer>> sortedEntries = new ArrayList<Map.Entry<Long, Integer>>(
                                        eventCounterMap.get(teamId).get(eventType).get(tagType).entrySet()
                                );
                                Collections.sort(sortedEntries, new Comparator<Map.Entry<Long, Integer>>() {
                                    @Override
                                    public int compare(Map.Entry<Long, Integer> e1, Map.Entry<Long, Integer> e2) {
                                        if (e2.getValue().equals(e1.getValue())) {
                                            return e1.getKey().compareTo(e2.getKey());
                                        } else {
                                            return e2.getValue().compareTo(e1.getValue()); // decrescente
                                        }
                                    }
                                });

                                // save into the eventRankingMap
                                eventRankingMap.putIfAbsent(teamId, new HashMap<Long, Map<String, List<PlayerRanking>>>());
                                eventRankingMap.get(teamId).putIfAbsent(eventType, new HashMap<String, List<PlayerRanking>>());
                                eventRankingMap.get(teamId).get(eventType).putIfAbsent(tagType, new ArrayList<PlayerRanking>());

                                for (Map.Entry<Long, Integer> entry : sortedEntries) {
                                    Player tmpPlayer;
                                    Long playerId = entry.getKey();
                                    if (players.containsKey(playerId)) {
                                        tmpPlayer = players.get(playerId);
                                    } else if (playersMap.containsKey(playerId)) {
                                        tmpPlayer = playersMap.get(playerId);
                                    } else {
                                        tmpPlayer = uService.getPlayer(playerId, true);
                                    }
                                    if (tmpPlayer != null) {
                                        PlayerRanking playerRanking = new PlayerRanking();
                                        playerRanking.setId(tmpPlayer.getId());
                                        playerRanking.setFirstName(tmpPlayer.getFirstName());
                                        playerRanking.setLastName(tmpPlayer.getLastName());
                                        playerRanking.setKnownName(tmpPlayer.getKnownName());
                                        playerRanking.setPhoto(tmpPlayer.getPhoto());
                                        playerRanking.setTeamId(teamId);
                                        playerRanking.setCounter(entry.getValue());

                                        eventRankingMap.get(teamId).get(eventType).get(tagType).add(playerRanking);
                                    }
                                }

                                // se non c'è già calcolo anche quello di entrambi i team
                                eventRankingMap.putIfAbsent(null, new HashMap<Long, Map<String, List<PlayerRanking>>>());
                                eventRankingMap.get(null).putIfAbsent(eventType, new HashMap<String, List<PlayerRanking>>());
                                eventRankingMap.get(null).get(eventType).putIfAbsent(tagType, new ArrayList<PlayerRanking>());
                                if (eventRankingMap.get(null).get(eventType).get(tagType).isEmpty()) {
                                    List<Map.Entry<Long, Integer>> tmpSortedEntries = new ArrayList<>();

                                    for (Long tmpTeamId : eventCounterMap.keySet()) {
                                        if (eventCounterMap.containsKey(tmpTeamId) && eventCounterMap.get(tmpTeamId).containsKey(eventType) && eventCounterMap.get(tmpTeamId).get(eventType).containsKey(tagType)) {
                                            tmpSortedEntries.addAll(eventCounterMap.get(tmpTeamId).get(eventType).get(tagType).entrySet());
                                        }
                                    }

                                    Collections.sort(tmpSortedEntries, new Comparator<Map.Entry<Long, Integer>>() {
                                        @Override
                                        public int compare(Map.Entry<Long, Integer> e1, Map.Entry<Long, Integer> e2) {
                                            if (e2.getValue().equals(e1.getValue())) {
                                                return e1.getKey().compareTo(e2.getKey());
                                            } else {
                                                return e2.getValue().compareTo(e1.getValue()); // decrescente
                                            }
                                        }
                                    });

                                    for (Map.Entry<Long, Integer> entry : tmpSortedEntries) {
                                        Player tmpPlayer;
                                        Long playerId = entry.getKey();
                                        if (players.containsKey(playerId)) {
                                            tmpPlayer = players.get(playerId);
                                        } else if (playersMap.containsKey(playerId)) {
                                            tmpPlayer = playersMap.get(playerId);
                                        } else {
                                            tmpPlayer = uService.getPlayer(playerId, true);
                                        }
                                        if (tmpPlayer != null) {
                                            PlayerRanking playerRanking = new PlayerRanking();
                                            playerRanking.setId(tmpPlayer.getId());
                                            playerRanking.setFirstName(tmpPlayer.getFirstName());
                                            playerRanking.setLastName(tmpPlayer.getLastName());
                                            playerRanking.setKnownName(tmpPlayer.getKnownName());
                                            playerRanking.setPhoto(tmpPlayer.getPhoto());
                                            // trovo il teamId giusto
                                            Long playerTeamId = null;
                                            for (Long tmpTeamId : eventCounterMap.keySet()) {
                                                if (eventCounterMap.containsKey(tmpTeamId) && eventCounterMap.get(tmpTeamId).containsKey(eventType) && eventCounterMap.get(tmpTeamId).get(eventType).containsKey(tagType)) {
                                                    if (eventCounterMap.get(tmpTeamId).get(eventType).get(tagType).containsKey(playerId)) {
                                                        playerTeamId = tmpTeamId;
                                                        break;
                                                    }
                                                }
                                            }
                                            playerRanking.setTeamId(playerTeamId);
                                            playerRanking.setCounter(entry.getValue());

                                            eventRankingMap.get(null).get(eventType).get(tagType).add(playerRanking);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    model.addAttribute("mEventRankings", eventRankingMap);

                    // SPOSTATO ALTRIMENTI DA ERRORE "TOO MUCH CODE"
                    populatePasses(groupedEvents, playersMap, fixtureDetail, model, homeTeamPlayers, awayTeamPlayers);
                    populateAnalysis(groupedEvents, playersMap, fixtureDetail, model, fixture, fixturePlayersMap, locale);

                    // SHOTS ANALYSIS - TEAM 1
                    List<Event> tmpEventList = new ArrayList();
                    long eventTypeId = 5L;
                    String tagType = null;
                    if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                            && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
                    }
                    model.addAttribute("mShotAnalysisT1Shots", MatchStudioHelper.drawFieldTiri(tmpEventList, fixturePlayersMap));
                    model.addAttribute("mShotAnalysisT1Timeline", MatchStudioHelper.drawTiriTimeline(fixtureDetail, tmpEventList, locale));

                    tmpEventList = new ArrayList();
                    eventTypeId = 28L;
                    tagType = null;
                    if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                            && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
                    }
                    model.addAttribute("mShotAnalysisT1Assists", MatchStudioHelper.drawFieldAssist(tmpEventList, fixturePlayersMap));

                    // SHOTS ANALYSIS - TEAM 2
                    tmpEventList = new ArrayList();
                    eventTypeId = 5L;
                    tagType = null;
                    if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                            && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        tmpEventList = groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType);
                    }
                    model.addAttribute("mShotAnalysisT2Shots", MatchStudioHelper.drawFieldTiri(tmpEventList, fixturePlayersMap));
                    model.addAttribute("mShotAnalysisT2Timeline", MatchStudioHelper.drawTiriTimeline(fixtureDetail, tmpEventList, locale));

                    tmpEventList = new ArrayList();
                    eventTypeId = 28L;
                    tagType = null;
                    if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                            && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        tmpEventList = groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType);
                    }
                    model.addAttribute("mShotAnalysisT2Assists", MatchStudioHelper.drawFieldAssist(tmpEventList, fixturePlayersMap));

                    populateOffensiveIndex(groupedEvents, playersMap, fixtureDetail, model, fixture, fixturePlayersMap);
                }

                // PAGINE GIOCATORI
                Map<Long, Map<String, Object>> playerDataMap = new LinkedHashMap<>();
                if (!playerIds.isEmpty()) {
                    for (Long playerId : playerIds) {
                        if (fixturePlayersMap.containsKey(playerId)) {
                            Map<String, Object> playerDataModel = new HashMap<>();
                            FixturePlayer fixturePlayer = fixturePlayersMap.get(playerId);

                            // PRIMA TABELLA
                            List<StudioTableRow> tab1Table = new ArrayList<>();
                            List<StudioTableRow> tab3Table = new ArrayList<>();
                            List<StudioTableRow> tab4Table = new ArrayList<>();
                            List<StudioTableRow> tab6Table = new ArrayList<>();
                            if (fixturePlayer.getPositionId() != null) {
                                playerDataModel.put("mPositionId", fixturePlayer.getPositionId());

                                if (Long.compare(fixturePlayer.getPositionId(), 1L) == 0) {
                                    // PORTIERE

                                    // Shots on target
                                    StudioTableRow row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tiri.in.porta", locale));
                                    Long eventTypeId = 6L;
                                    String tagType = "53";
                                    StudioTableValue value = new StudioTableValue();
                                    StudioTableValue secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // Saves held
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.parate.trattenuta", locale));
                                    eventTypeId = 16L;
                                    tagType = "1106";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // Saves rejected
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.parate.respinte", locale));
                                    eventTypeId = 16L;
                                    tagType = "1107";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // Goals against
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.gol.subiti", locale));
                                    eventTypeId = 26L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // % Saves
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.parate", locale));
                                    eventTypeId = 16L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "53";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    StudioTableValue tmpPercentage = new StudioTableValue();
                                    tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                    tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                    tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                    row.setType(2); // percentuale
                                    row.getValues().add(tmpPercentage);
                                    tab1Table.add(row);
                                    // Throws by foot / hand
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.rinvii.di.piede.mano", locale));
                                    eventTypeId = 14L;
                                    tagType = "87";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "89";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "88";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    row.getValues().add(secondValue);
                                    tab1Table.add(row);
                                    // Average lenght Throws by foot / hand
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.lunghezza.media.del.rinvio.di.piede.mano", locale));
                                    eventTypeId = 14L;
                                    tagType = "87";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer, ValueType.DISTANCE);
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "89";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer, ValueType.DISTANCE);
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "88";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer, ValueType.DISTANCE);
                                        }
                                    }
                                    value.setTotal((int) Math.round(value.getTmpValue() / (value.getTotal() > 0 ? value.getTotal() : 1)));
                                    value.setFirstHalf((int) Math.round(value.getTmpValueFirstHalf() / (value.getFirstHalf() > 0 ? value.getFirstHalf() : 1)));
                                    value.setSecondHalf((int) Math.round(value.getTmpValueSecondHalf() / (value.getSecondHalf() > 0 ? value.getSecondHalf() : 1)));
                                    row.getValues().add(value);
                                    secondValue.setTotal((int) Math.round(secondValue.getTmpValue() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1)));
                                    secondValue.setFirstHalf((int) Math.round(secondValue.getTmpValueFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1)));
                                    secondValue.setSecondHalf((int) Math.round(secondValue.getTmpValueSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1)));
                                    row.getValues().add(secondValue);
                                    tab1Table.add(row);
                                    // Killer Passes
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave", locale));
                                    eventTypeId = 12L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // Decisive Action Save / Claim
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.interventi.decisivi.in.parata.uscita", locale));
                                    eventTypeId = 16L;
                                    tagType = "99";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    eventTypeId = 15L;
                                    tagType = "98";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    row.getValues().add(secondValue);
                                    tab1Table.add(row);
                                    // Touches out of the penalty area
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.fuori.area", locale));
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                        for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                            if (BooleanUtils.isFalse(event.getInArea())) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);
                                    // GK Claims
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.uscite", locale));
                                    eventTypeId = 15L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab1Table.add(row);

                                    // SECONDA TABELLA
                                    List<StudioTableRow> tab2Table = new ArrayList<>();
                                    // Shots against
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tiri.subiti", locale));
                                    eventTypeId = 6L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab2Table.add(row);
                                    // From penalty area
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.da.area", locale));
                                    eventTypeId = 6L;
                                    tagType = "55";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab2Table.add(row);
                                    // From outside the penalty
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.da.fuori.area", locale));
                                    eventTypeId = 6L;
                                    tagType = "56";
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab2Table.add(row);
                                    // On open play
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.su.azione", locale));
                                    eventTypeId = 6L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(2L)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(2L).containsKey(null)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (isActionInside(event, groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(2L).get(null))) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab2Table.add(row);
                                    // On set pieces
                                    row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.su.palla.inattiva", locale));
                                    eventTypeId = 6L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(4L)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(4L).containsKey(null)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (isActionInside(event, groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(4L).get(null))) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                    }
                                    row.getValues().add(value);
                                    tab2Table.add(row);

                                    playerDataModel.put("mTab2Table", tab2Table);

                                    List<Event> retiSubitePortiereSubList = new ArrayList<>(),
                                            tiriInPortaSubitiPortiereSubList = new ArrayList<>(),
                                            tiriSubitiFuoriPortaPortiereSubList = new ArrayList<>();
                                    eventTypeId = 26L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 1) {
                                                    retiSubitePortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "53";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 1) {
                                                    tiriInPortaSubitiPortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "54";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 1) {
                                                    tiriSubitiFuoriPortaPortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    String tab2FirstImagePort = null;
                                    if (tab2FirstImagePort == null) {
                                        tab2FirstImagePort = MatchStudioHelper.drawFieldTiriByLists(retiSubitePortiereSubList, tiriInPortaSubitiPortiereSubList, tiriSubitiFuoriPortaPortiereSubList);
                                    }
                                    playerDataModel.put("mTab2FirstImage", tab2FirstImagePort);

                                    retiSubitePortiereSubList = new ArrayList<>();
                                    tiriInPortaSubitiPortiereSubList = new ArrayList<>();
                                    tiriSubitiFuoriPortaPortiereSubList = new ArrayList<>();
                                    eventTypeId = 26L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 2) {
                                                    retiSubitePortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "53";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 2) {
                                                    tiriInPortaSubitiPortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "54";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                if (event.getPeriod() != null && event.getPeriod() == 2) {
                                                    tiriSubitiFuoriPortaPortiereSubList.add(event);
                                                }
                                            }
                                        }
                                    }
                                    String tab2SecondImagePort = null;
                                    if (tab2SecondImagePort == null) {
                                        tab2SecondImagePort = MatchStudioHelper.drawFieldTiriByLists(retiSubitePortiereSubList, tiriInPortaSubitiPortiereSubList, tiriSubitiFuoriPortaPortiereSubList);
                                    }
                                    playerDataModel.put("mTab2SecondImage", tab2SecondImagePort);

                                    retiSubitePortiereSubList = new ArrayList<>();
                                    tiriInPortaSubitiPortiereSubList = new ArrayList<>();
                                    tiriSubitiFuoriPortaPortiereSubList = new ArrayList<>();
                                    eventTypeId = 26L;
                                    tagType = null;
                                    value = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                retiSubitePortiereSubList.add(event);
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "53";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                tiriInPortaSubitiPortiereSubList.add(event);
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "54";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                tiriSubitiFuoriPortaPortiereSubList.add(event);
                                            }
                                        }
                                    }
                                    eventTypeId = 6L;
                                    tagType = "52";
                                    secondValue = new StudioTableValue();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            // escludo bloccati
                                            if (!event.getTagTypeIds().contains(44L)) {
                                                tiriSubitiFuoriPortaPortiereSubList.add(event);
                                            }
                                        }
                                    }
                                    String tab2ThirdImagePort = null;
                                    if (tab2ThirdImagePort == null) {
                                        tab2ThirdImagePort = MatchStudioHelper.drawFieldTiriPortaByLists(retiSubitePortiereSubList, tiriInPortaSubitiPortiereSubList, tiriSubitiFuoriPortaPortiereSubList);
                                    }
                                    playerDataModel.put("mTab2ThirdImage", tab2ThirdImagePort);

                                    // TERZA TABELLA
                                    // Passes and long balls
                                    Map<Long, StudioTableRow> tab2Players = new HashMap<>();
                                    eventTypeId = 14L;
                                    tagType = "12";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab2Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab2Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab2Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab2Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab2Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab2Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab2Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab2Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab2Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab2Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (Long tmpPlayerId : tab2Players.keySet()) {
                                        tab3Table.add(tab2Players.get(tmpPlayerId));
                                    }

                                    Collections.sort(tab3Table, new Comparator<StudioTableRow>() {
                                        @Override
                                        public int compare(StudioTableRow o1, StudioTableRow o2) {
                                            if (Long.compare(o1.getValues().get(0).getTotal(), o2.getValues().get(0).getTotal()) == 0) {
                                                if (Long.compare(o1.getValues().get(0).getFirstHalf(), o2.getValues().get(0).getFirstHalf()) == 0) {
                                                    if (Long.compare(o1.getValues().get(0).getSecondHalf(), o2.getValues().get(0).getSecondHalf()) == 0) {
                                                        return o1.getText().compareTo(o2.getText());
                                                    } else {
                                                        return o2.getValues().get(0).getSecondHalf().compareTo(o1.getValues().get(0).getSecondHalf());
                                                    }
                                                } else {
                                                    return o2.getValues().get(0).getFirstHalf().compareTo(o1.getValues().get(0).getFirstHalf());
                                                }
                                            } else {
                                                return o2.getValues().get(0).getTotal().compareTo(o1.getValues().get(0).getTotal());
                                            }
                                        }
                                    });
                                    if (tab3Table.size() > 15) {
                                        tab3Table = tab3Table.subList(0, 14);
                                    }

                                    List<Event> tab3ImageOrangeData = new ArrayList<>();
                                    List<Event> tab3ImageBlueData = new ArrayList<>();
                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                if (event.getDistance() != null) {
                                                    if (event.getDistance() > 40) {
                                                        tab3ImageBlueData.add(event);
                                                    } else {
                                                        tab3ImageOrangeData.add(event);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    String tab3ImagePort = null;
                                    if (tab3ImagePort == null) {
                                        tab3ImagePort = MatchStudioHelper.drawFieldWithArrowByActionList(tab3ImageOrangeData, tab3ImageBlueData, fixturePlayer.getJerseyNumber().toString(), fixturePlayersMap);
                                    }
                                    playerDataModel.put("mTab3Image", tab3ImagePort);

                                    // QUARTA TABELLA
                                    // Goal Kicks
                                    Map<Long, StudioTableRow> tab4Players = new HashMap<>();
                                    eventTypeId = 14L;
                                    tagType = "89";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab4Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab4Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab4Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab4Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab4Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "88";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab4Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab4Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab4Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab4Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab4Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "87";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab4Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab4Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab4Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab4Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab4Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (Long tmpPlayerId : tab4Players.keySet()) {
                                        tab4Table.add(tab4Players.get(tmpPlayerId));
                                    }

                                    Collections.sort(tab4Table, new Comparator<StudioTableRow>() {
                                        @Override
                                        public int compare(StudioTableRow o1, StudioTableRow o2) {
                                            if (Long.compare(o1.getValues().get(0).getTotal(), o2.getValues().get(0).getTotal()) == 0) {
                                                if (Long.compare(o1.getValues().get(0).getFirstHalf(), o2.getValues().get(0).getFirstHalf()) == 0) {
                                                    if (Long.compare(o1.getValues().get(0).getSecondHalf(), o2.getValues().get(0).getSecondHalf()) == 0) {
                                                        return o1.getText().compareTo(o2.getText());
                                                    } else {
                                                        return o2.getValues().get(0).getSecondHalf().compareTo(o1.getValues().get(0).getSecondHalf());
                                                    }
                                                } else {
                                                    return o2.getValues().get(0).getFirstHalf().compareTo(o1.getValues().get(0).getFirstHalf());
                                                }
                                            } else {
                                                return o2.getValues().get(0).getTotal().compareTo(o1.getValues().get(0).getTotal());
                                            }
                                        }
                                    });
                                    if (tab4Table.size() > 15) {
                                        tab4Table = tab4Table.subList(0, 14);
                                    }

                                    List<Event> tab4ImageOrangeData = new ArrayList<>();
                                    List<Event> tab4ImageBlueData = new ArrayList<>();
                                    List<Event> tab4ImageCyanData = new ArrayList<>();
                                    eventTypeId = 14L;
                                    tagType = "88";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                tab4ImageOrangeData.add(event);
                                            }
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "89";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                tab4ImageBlueData.add(event);
                                            }
                                        }
                                    }
                                    eventTypeId = 14L;
                                    tagType = "87";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                tab4ImageCyanData.add(event);
                                            }
                                        }
                                    }
                                    String tab4ImagePort = null;
                                    if (tab4ImagePort == null) {
                                        tab4ImagePort = MatchStudioHelper.drawFieldWithArrowByActionList(tab4ImageOrangeData, tab4ImageBlueData, tab4ImageCyanData, fixturePlayer.getJerseyNumber().toString(), fixturePlayersMap);
                                    }
                                    playerDataModel.put("mTab4Image", tab4ImagePort);

                                    // QUINTA TABELLA
                                    List<Event> tab5FirstImageData = new ArrayList<>();
                                    List<Event> tab5SecondImageData = new ArrayList<>();
                                    eventTypeId = 101L;
                                    tagType = null;
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null && event.getPeriod() != null) {
                                                if (event.getTagTypeIds() == null || !event.getTagTypeIds().contains(2401L)) {
                                                    if (isPlaying(event, fixtureDetail, fixturePlayer)) {
                                                        if (event.getPeriod() == 1) {
                                                            tab5FirstImageData.add(event);
                                                        } else if (event.getPeriod() == 2) {
                                                            tab5SecondImageData.add(event);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    String tab5FirstImagePort = null, tab5SecondImagePort = null;
                                    if (tab5FirstImagePort == null) {
                                        tab5FirstImagePort = MatchStudioHelper.drawFieldPalleLateraliByList(tab5FirstImageData);
                                        tab5SecondImagePort = MatchStudioHelper.drawFieldPalleLateraliByList(tab5SecondImageData);
                                    }
                                    playerDataModel.put("mTab5FirstImage", tab5FirstImagePort);
                                    playerDataModel.put("mTab5SecondImage", tab5SecondImagePort);

                                    // SESTA TABELLA
                                    // Passes
                                    Map<Long, StudioTableRow> tab6Players = new HashMap<>();
                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab6Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab6Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab6Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab6Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab6Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (Long tmpPlayerId : tab6Players.keySet()) {
                                        tab6Table.add(tab6Players.get(tmpPlayerId));
                                    }

                                    Collections.sort(tab6Table, new Comparator<StudioTableRow>() {
                                        @Override
                                        public int compare(StudioTableRow o1, StudioTableRow o2) {
                                            if (Long.compare(o1.getValues().get(0).getTotal(), o2.getValues().get(0).getTotal()) == 0) {
                                                if (Long.compare(o1.getValues().get(0).getFirstHalf(), o2.getValues().get(0).getFirstHalf()) == 0) {
                                                    if (Long.compare(o1.getValues().get(0).getSecondHalf(), o2.getValues().get(0).getSecondHalf()) == 0) {
                                                        return o1.getText().compareTo(o2.getText());
                                                    } else {
                                                        return o2.getValues().get(0).getSecondHalf().compareTo(o1.getValues().get(0).getSecondHalf());
                                                    }
                                                } else {
                                                    return o2.getValues().get(0).getFirstHalf().compareTo(o1.getValues().get(0).getFirstHalf());
                                                }
                                            } else {
                                                return o2.getValues().get(0).getTotal().compareTo(o1.getValues().get(0).getTotal());
                                            }
                                        }
                                    });
                                    if (tab6Table.size() > 15) {
                                        tab6Table = tab6Table.subList(0, 14);
                                    }

                                    String tab6ImagePort = null;
                                    if (tab6ImagePort == null) {
                                        List<String> top3Position = new ArrayList<>();
                                        for (StudioTableRow tmpRow : tab6Table) {
                                            if (tmpRow.getNumber() != null) {
                                                if (top3Position.size() < 3) {
                                                    top3Position.add(tmpRow.getNumber().toString());
                                                } else {
                                                    break;
                                                }
                                            }
                                        }
                                        String module = fixture.getHomeModule().replace("-", "");
                                        if (Long.compare(fixture.getAwayTeamId(), fixturePlayer.getTeamId()) == 0) {
                                            module = fixture.getAwayModule().replace("-", "");
                                        }
                                        tab6ImagePort = MatchStudioHelper.drawFieldFormazioniVerticale(top3Position, fixturePlayer.getTeamId(), module, fixturePlayersMap);
                                    }
                                    playerDataModel.put("mTab6Image", tab6ImagePort);

                                    // SETTIMA TABELLA
                                    List<Event> tmpEventList = new ArrayList<>();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(fixturePlayer.getPlayerId())) {
                                        tmpEventList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId));
                                    }
                                    MatchStudioHeatMap myMap1 = MatchStudioHelper.buildHeatMapFromActionList(tmpEventList);
                                    BufferedImage heatMapT1 = myMap1.createHeatMap(0.9f);
                                    heatMapT1 = MatchStudioHelper.rotateImageBy(heatMapT1, 90);
                                    try {
                                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                        ImageIO.write(heatMapT1, "png", baos);
                                        String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                                        playerDataModel.put("mTab7Image", base64Image);
                                    } catch (IOException ex) {
                                        GlobalHelper.reportError(ex);
                                    }

                                    // OTTAVA TABELLA
                                    JsonArray jsonArray = new JsonArray();
                                    int halfDuration = MatchStudioHelper.getHalfDuration(fixtureDetail);
                                    int halfInterval = halfDuration / 3;

                                    tmpEventList = new ArrayList<>();
                                    List<Event> tmpPlayerEventList = new ArrayList<>();
                                    for (FixturePlayer tmpFixturePlayer : fixturePlayers) {
                                        if (tmpFixturePlayer.getTeamId() != null && Long.compare(tmpFixturePlayer.getTeamId(), fixturePlayer.getTeamId()) == 0) {
                                            if (tmpFixturePlayer.getPositionId() != null && tmpFixturePlayer.getPositionId() != 1) { // niente portieri
                                                if (groupedEvents.containsKey(tmpFixturePlayer.getTeamId()) && groupedEvents.get(tmpFixturePlayer.getTeamId()).containsKey(tmpFixturePlayer.getPlayerId())) {
                                                    tmpEventList.addAll(getPlayerTouches(groupedEvents.get(tmpFixturePlayer.getTeamId()).get(tmpFixturePlayer.getPlayerId())));
                                                }
                                            }
                                        }
                                    }
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(fixturePlayer.getPlayerId())) {
                                        tmpPlayerEventList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId));
                                    }

                                    // calcolo primo e secondo tempo
                                    for (int i = 1; i <= 6; i++) {
                                        long maxStartAzione, minStartAzione = 0;
                                        List<Long> validPlayers = new ArrayList<>();
                                        if (i <= 3) {
                                            if (i == 3) {
                                                maxStartAzione = fixtureDetail.getEndTime1();
                                            } else {
                                                maxStartAzione = fixtureDetail.getStartTime1() + ((halfInterval * 60 * 1000) * i);
                                            }
                                            if (i > 0) {
                                                minStartAzione = fixtureDetail.getStartTime1() + ((halfInterval * 60 * 1000) * (i - 1));
                                            }
                                        } else {
                                            if (i == 6) {
                                                maxStartAzione = fixtureDetail.getEndTime2();
                                            } else {
                                                maxStartAzione = fixtureDetail.getStartTime2() + ((halfInterval * 60 * 1000) * (i - 3));
                                            }
                                            minStartAzione = fixtureDetail.getStartTime2() + ((halfInterval * 60 * 1000) * (i - 4));
                                        }

                                        int totalTeam = 0, totalPlayer = 0;
                                        for (Event tmpEvent : tmpEventList) {
                                            if (tmpEvent.getStartMillis() != null && tmpEvent.getEndMillis() != null) {
                                                if (tmpEvent.getStartMillis() >= minStartAzione && tmpEvent.getStartMillis() <= maxStartAzione) {
                                                    totalTeam++;
                                                    if (tmpEvent.getPlayerIds() != null) {
                                                        for (Long tmpPlayerId : tmpEvent.getPlayerIds()) {
                                                            if (!validPlayers.contains(tmpPlayerId)) {
                                                                validPlayers.add(tmpPlayerId);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        for (Event tmpEvent : tmpPlayerEventList) {
                                            if (tmpEvent.getStartMillis() != null && tmpEvent.getEndMillis() != null) {
                                                if (tmpEvent.getStartMillis() >= minStartAzione && tmpEvent.getStartMillis() <= maxStartAzione) {
                                                    totalPlayer++;
                                                }
                                            }
                                        }

                                        JsonObject element = new JsonObject();
                                        element.addProperty("year", halfInterval * i);
                                        if (!validPlayers.isEmpty()) {
                                            element.addProperty("value", totalTeam / validPlayers.size());
                                        }
                                        if (totalPlayer > 0) {
                                            element.addProperty("value2", totalPlayer);
                                        }

                                        jsonArray.add(element);
                                    }

                                    playerDataModel.put("mTab8Data", jsonArray.toString());
                                } else if (Long.compare(fixturePlayer.getPositionId(), 1L) != 0) {
                                    // TUTTI GLI ALTRI
                                    if (Long.compare(fixturePlayer.getPositionId(), 2L) == 0) {
                                        // DIFENSORE

                                        // Positive Passes / Total
                                        StudioTableRow row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.riusciti.totali", locale));
                                        Long eventTypeId = 34L;
                                        String tagType = "416";
                                        StudioTableValue value = new StudioTableValue();
                                        StudioTableValue secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // % Positive Passes
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.passaggi.riusciti", locale));
                                        StudioTableValue tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // Killer Passes
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave", locale));
                                        eventTypeId = 12L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Passes received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.ricevuti", locale));
                                        eventTypeId = 34L;
                                        tagType = "416";
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Touches own area / def. third
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.palla.propria.area.terzo.difensivo", locale));
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                            for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                                if (event.getThird() != null && event.getThird() == 1) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                    if (BooleanUtils.isTrue(event.getInArea())) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Touches in opponent half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.meta.offensiva", locale));
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                            for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Ball Recoveries
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate", locale));
                                        eventTypeId = 11L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Ball recoveries in offensive half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate.meta.offensiva", locale));
                                        eventTypeId = 11L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // % Duels won
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.duelli.vinti", locale));
                                        eventTypeId = 9L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 23L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // % Duels won in offensive half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.duelli.vinti.meta.offensiva", locale));
                                        eventTypeId = 9L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        eventTypeId = 23L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // Duels won / lost in own area
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.persi.meta.offensiva", locale));
                                        eventTypeId = 9L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getThird() != null && event.getThird() == 1) {
                                                    if (BooleanUtils.isTrue(event.getInArea())) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        eventTypeId = 23L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getThird() != null && event.getThird() == 1) {
                                                    if (BooleanUtils.isTrue(event.getInArea())) {
                                                        increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Dribbling suffered / won
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.dribbling.vinti.totali", locale));
                                        // TODO MANCA TAG POSITIVO SU DRIBBLING
//                            eventTypeId = 22L;
//                            tagType = null;
//                            value = new StudioTableValue();
//                            secondValue = new StudioTableValue();
//                            if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
//                                    && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
//                                    && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
//                                for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
//                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
//                                }
//                            }
//                            eventTypeId = 8L;
//                            tagType = null;
//                            if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
//                                    && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
//                                    && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
//                                for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
//                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
//                                }
//                            }
//                            row.getValues().add(value);
//                            row.getValues().add(secondValue);
//                            tab1Table.add(row);

                                        // SECONDA TABELLA
                                        JsonArray eventData = new JsonArray();
                                        JsonArray playerData = new JsonArray();
                                        JsonArray averageData = new JsonArray();
                                        // Killer Passes
                                        StudioRadarItem radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 12L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        JsonObject element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Passes
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.fatti", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Touches def. third
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.tocchi.terzo.dif", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        for (FixturePlayer tmpFixturePlayer : fixturePlayers) {
                                            if (tmpFixturePlayer.getTeamId() != null && Long.compare(tmpFixturePlayer.getTeamId(), fixturePlayer.getTeamId()) == 0) {
                                                if (tmpFixturePlayer.getPositionId() != null && tmpFixturePlayer.getPositionId() != 1) { // niente portieri
                                                    if (groupedEvents.containsKey(tmpFixturePlayer.getTeamId()) && groupedEvents.get(tmpFixturePlayer.getTeamId()).containsKey(tmpFixturePlayer.getPlayerId())) {
                                                        for (Event event : getPlayerTouches(groupedEvents.get(tmpFixturePlayer.getTeamId()).get(tmpFixturePlayer.getPlayerId()))) {
                                                            if (event.getThird() != null && event.getThird() == 1) {
                                                                if (event.getPlayerIds() != null) {
                                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Duels won in offensive half
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.meta.off", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    if (event.getPlayerIds() != null) {
                                                        for (Long tmpPlayerId : event.getPlayerIds()) {
                                                            radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Ball recoveries in offensive half
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate.meta.off", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 11L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    if (event.getPlayerIds() != null) {
                                                        for (Long tmpPlayerId : event.getPlayerIds()) {
                                                            radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Duels Won
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.lower", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        playerDataModel.put("mTab2DataEvent", eventData.toString());
                                        playerDataModel.put("mTab2DataPlayer", playerData.toString());
                                        playerDataModel.put("mTab2DataAverage", averageData.toString());
                                    } else if (Long.compare(fixturePlayer.getPositionId(), 3L) == 0) {
                                        // CENTROCAMPISTA

                                        // Positive Passes / Total
                                        StudioTableRow row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.riusciti.totali", locale));
                                        Long eventTypeId = 34L;
                                        String tagType = "416";
                                        StudioTableValue value = new StudioTableValue();
                                        StudioTableValue secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // % Positive Passes
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.passaggi.riusciti", locale));
                                        StudioTableValue tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // Killer Passes / Assists
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave.assist", locale));
                                        eventTypeId = 12L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 28L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Passes received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.ricevuti", locale));
                                        eventTypeId = 34L;
                                        tagType = "416";
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Killer Passes Received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave.ricevuti", locale));
                                        eventTypeId = 12L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // One Two in opponent half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.uno.due.meta.offensiva", locale));
                                        eventTypeId = 102L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Touches own area / def. third
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.palla.propria.area.terzo.difensivo", locale));
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                            for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                                if (event.getThird() != null && event.getThird() == 1) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                    if (BooleanUtils.isTrue(event.getInArea())) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Positive Side Balls / Total
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.laterali.riuscite.totali", locale));
                                        eventTypeId = 100L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 100L;
                                        tagType = "5463";
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // % Positive Side Balls
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.palle.laterali.riuscite", locale));
                                        tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // Dribbling Won / Total
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.dribbling.vinti.totali", locale));
                                        eventTypeId = 9L;
                                        tagType = "68";
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 8L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Shots from Penalty area / Total
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tiri.da.area.totali", locale));
                                        eventTypeId = 5L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                if (BooleanUtils.isTrue(event.getInArea())) {
                                                    if (event.getThird() != null && event.getThird() == 3) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Ball Recoveries
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate", locale));
                                        eventTypeId = 11L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Ball recoveries in offensive half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate.meta.offensiva", locale));
                                        eventTypeId = 11L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // % Duels won
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.duelli.vinti", locale));
                                        eventTypeId = 9L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 23L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);

                                        // SECONDA TABELLA
                                        JsonArray eventData = new JsonArray();
                                        JsonArray playerData = new JsonArray();
                                        JsonArray averageData = new JsonArray();
                                        // Killer Passes
                                        StudioRadarItem radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 12L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        JsonObject element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Passes
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.fatti", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Touches off. third
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.tocchi.terzo.off", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        for (FixturePlayer tmpFixturePlayer : fixturePlayers) {
                                            if (tmpFixturePlayer.getTeamId() != null && Long.compare(tmpFixturePlayer.getTeamId(), fixturePlayer.getTeamId()) == 0) {
                                                if (tmpFixturePlayer.getPositionId() != null && tmpFixturePlayer.getPositionId() != 1) { // niente portieri
                                                    if (groupedEvents.containsKey(tmpFixturePlayer.getTeamId()) && groupedEvents.get(tmpFixturePlayer.getTeamId()).containsKey(tmpFixturePlayer.getPlayerId())) {
                                                        for (Event event : getPlayerTouches(groupedEvents.get(tmpFixturePlayer.getTeamId()).get(tmpFixturePlayer.getPlayerId()))) {
                                                            if (event.getThird() != null && event.getThird() == 3) {
                                                                if (event.getPlayerIds() != null) {
                                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Shots
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.tiri", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 5L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Dribblings
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.dribbling", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 8L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Duels Won
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.lower", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        playerDataModel.put("mTab2DataEvent", eventData.toString());
                                        playerDataModel.put("mTab2DataPlayer", playerData.toString());
                                        playerDataModel.put("mTab2DataAverage", averageData.toString());
                                    } else if (Long.compare(fixturePlayer.getPositionId(), 4L) == 0) {
                                        // ATTACCANTE

                                        // Positive Passes in opponent half / Total
                                        StudioTableRow row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.riusciti.meta.offensiva.totali", locale));
                                        Long eventTypeId = 34L;
                                        String tagType = "416";
                                        StudioTableValue value = new StudioTableValue();
                                        StudioTableValue secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Killer Passes / Assists
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave.assist", locale));
                                        eventTypeId = 12L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 28L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Touches own area / def. third
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.palla.propria.area.terzo.difensivo", locale));
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                            for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                                if (event.getThird() != null && event.getThird() == 1) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                    if (BooleanUtils.isTrue(event.getInArea())) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Passes received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.ricevuti", locale));
                                        eventTypeId = 34L;
                                        tagType = "416";
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Killer Passes Received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave.ricevuti", locale));
                                        eventTypeId = 12L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // One Two in opponent half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.uno.due.meta.offensiva", locale));
                                        eventTypeId = 102L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Side Balls Received
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.laterali.ricevute", locale));
                                        eventTypeId = 100L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    if (event.getPlayerToIds().contains(playerId)) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Dribbling Won / Total
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.dribbling.vinti.totali", locale));
                                        eventTypeId = 9L;
                                        tagType = "68";
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 8L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Shots from Penalty area / Total
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tiri.da.area.totali", locale));
                                        eventTypeId = 5L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                if (BooleanUtils.isTrue(event.getInArea())) {
                                                    if (event.getThird() != null && event.getThird() == 3) {
                                                        increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // % Shots on Goal
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.percentuale.realizzazione.gol", locale));
                                        eventTypeId = 5L;
                                        tagType = "30";
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        eventTypeId = 5L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                            }
                                        }
                                        StudioTableValue tmpPercentage = new StudioTableValue();
                                        tmpPercentage.setTotal((int) Math.round(1D * value.getTotal() / (secondValue.getTotal() > 0 ? secondValue.getTotal() : 1) * 100));
                                        tmpPercentage.setFirstHalf((int) Math.round(1D * value.getFirstHalf() / (secondValue.getFirstHalf() > 0 ? secondValue.getFirstHalf() : 1) * 100));
                                        tmpPercentage.setSecondHalf((int) Math.round(1D * value.getSecondHalf() / (secondValue.getSecondHalf() > 0 ? secondValue.getSecondHalf() : 1) * 100));
                                        row.setType(2); // percentuale
                                        row.getValues().add(tmpPercentage);
                                        tab1Table.add(row);
                                        // Ball recoveries in offensive half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate.meta.offensiva", locale));
                                        eventTypeId = 11L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);
                                        // Duels Won / Lost in offensive half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.persi.meta.offensiva", locale));
                                        eventTypeId = 9L;
                                        tagType = null;
                                        value = new StudioTableValue();
                                        secondValue = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        eventTypeId = 23L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    increaseTableValue(secondValue, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        row.getValues().add(secondValue);
                                        tab1Table.add(row);
                                        // Touches in own half
                                        row = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.tocchi.meta.difensiva", locale));
                                        value = new StudioTableValue();
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                            for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                                if (event.getHalf() != null && event.getHalf() == 1) {
                                                    increaseTableValue(value, event, fixtureDetail, fixturePlayer);
                                                }
                                            }
                                        }
                                        row.getValues().add(value);
                                        tab1Table.add(row);

                                        // SECONDA TABELLA
                                        JsonArray eventData = new JsonArray();
                                        JsonArray playerData = new JsonArray();
                                        JsonArray averageData = new JsonArray();
                                        // Killer Passes Received
                                        StudioRadarItem radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.chiave.ricevuti", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 12L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerToIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        JsonObject element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Passes
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.passaggi.fatti", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 34L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Touches opp. area
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.tocchi.area.avv", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        for (FixturePlayer tmpFixturePlayer : fixturePlayers) {
                                            if (tmpFixturePlayer.getTeamId() != null && Long.compare(tmpFixturePlayer.getTeamId(), fixturePlayer.getTeamId()) == 0) {
                                                if (tmpFixturePlayer.getPositionId() != null && tmpFixturePlayer.getPositionId() != 1) { // niente portieri
                                                    if (groupedEvents.containsKey(tmpFixturePlayer.getTeamId()) && groupedEvents.get(tmpFixturePlayer.getTeamId()).containsKey(tmpFixturePlayer.getPlayerId())) {
                                                        for (Event event : getPlayerTouches(groupedEvents.get(tmpFixturePlayer.getTeamId()).get(tmpFixturePlayer.getPlayerId()))) {
                                                            if (BooleanUtils.isTrue(event.getInArea())) {
                                                                if (event.getThird() != null && event.getThird() == 3) {
                                                                    if (event.getPlayerIds() != null) {
                                                                        for (Long tmpPlayerId : event.getPlayerIds()) {
                                                                            radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Shots
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.tiri", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 5L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Ball Recoveries in off. half
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.palle.recuperate.meta.off", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 11L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getHalf() != null && event.getHalf() == 2) {
                                                    if (event.getPlayerIds() != null) {
                                                        for (Long tmpPlayerId : event.getPlayerIds()) {
                                                            radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        // Duels Won
                                        radarItem = new StudioRadarItem(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.lower", locale));
                                        radarItem.setTotalPlayers(new ArrayList<>(fixturePlayersMap.values()), fixturePlayer.getTeamId());
                                        eventTypeId = 9L;
                                        tagType = null;
                                        if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                                && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                            for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                                if (event.getPlayerIds() != null) {
                                                    for (Long tmpPlayerId : event.getPlayerIds()) {
                                                        radarItem.getPlayersValueMap().put(tmpPlayerId, radarItem.getPlayersValueMap().getOrDefault(tmpPlayerId, 0) + 1);
                                                    }
                                                }
                                            }
                                        }
                                        if (radarItem.getTotalPlayers() != null && radarItem.getTotalPlayers() > 0) {
                                            radarItem.setAverageValue(1D * radarItem.getTotalPlayerValue() / radarItem.getTotalPlayers());
                                        }
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        eventData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        if (radarItem.getMaxPlayerValue() > 0) {
                                            element.addProperty("value", Math.round(1D * (radarItem.getPlayersValueMap().getOrDefault(playerId, 0)) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        } else {
                                            element.addProperty("value", 0);
                                        }
                                        playerData.add(element);
                                        element = new JsonObject();
                                        element.addProperty("event", radarItem.getText() + " (" + radarItem.getPlayersValueMap().getOrDefault(playerId, 0) + ")");
                                        element.addProperty("value", Math.round(1D * (radarItem.getAverageValue()) / (radarItem.getMaxPlayerValue()) * 100D) / 100D);
                                        averageData.add(element);

                                        playerDataModel.put("mTab2DataEvent", eventData.toString());
                                        playerDataModel.put("mTab2DataPlayer", playerData.toString());
                                        playerDataModel.put("mTab2DataAverage", averageData.toString());
                                    }

                                    // TERZA TABELLA
                                    // Passes
                                    Map<Long, StudioTableRow> tab3Players = new HashMap<>();
                                    Long eventTypeId = 14L;
                                    String tagType = "12";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab3Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab3Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab3Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab3Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab3Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerToIds()) {
                                                    Player tmpPlayer;
                                                    if (players.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = players.get(tmpPlayerId);
                                                    } else if (playersMap.containsKey(tmpPlayerId)) {
                                                        tmpPlayer = playersMap.get(tmpPlayerId);
                                                    } else {
                                                        tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                    }
                                                    if (tmpPlayer != null) {
                                                        tab3Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                        playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                        if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                            tab3Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                        }
                                                        if (tab3Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                            tab3Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                        }

                                                        increaseTableValue(tab3Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (Long tmpPlayerId : tab3Players.keySet()) {
                                        tab3Table.add(tab3Players.get(tmpPlayerId));
                                    }

                                    Collections.sort(tab3Table, new Comparator<StudioTableRow>() {
                                        @Override
                                        public int compare(StudioTableRow o1, StudioTableRow o2) {
                                            if (Long.compare(o1.getValues().get(0).getTotal(), o2.getValues().get(0).getTotal()) == 0) {
                                                if (Long.compare(o1.getValues().get(0).getFirstHalf(), o2.getValues().get(0).getFirstHalf()) == 0) {
                                                    if (Long.compare(o1.getValues().get(0).getSecondHalf(), o2.getValues().get(0).getSecondHalf()) == 0) {
                                                        return o1.getText().compareTo(o2.getText());
                                                    } else {
                                                        return o2.getValues().get(0).getSecondHalf().compareTo(o1.getValues().get(0).getSecondHalf());
                                                    }
                                                } else {
                                                    return o2.getValues().get(0).getFirstHalf().compareTo(o1.getValues().get(0).getFirstHalf());
                                                }
                                            } else {
                                                return o2.getValues().get(0).getTotal().compareTo(o1.getValues().get(0).getTotal());
                                            }
                                        }
                                    });
                                    if (tab3Table.size() > 15) {
                                        tab3Table = tab3Table.subList(0, 14);
                                    }

                                    List<Event> tab3ImageOrangeData = new ArrayList<>();
                                    List<Event> tab3ImageBlueData = new ArrayList<>();
                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerToIds() != null) {
                                                tab3ImageOrangeData.add(event);
                                            }
                                        }
                                    }
                                    String tab3ImagePort = null;
                                    if (tab3ImagePort == null) {
                                        tab3ImagePort = MatchStudioHelper.drawFieldWithArrowByActionList(tab3ImageOrangeData, tab3ImageBlueData, fixturePlayer.getJerseyNumber().toString(), fixturePlayersMap);
                                    }
                                    playerDataModel.put("mTab3Image", tab3ImagePort);

                                    // QUARTA TABELLA
                                    // Passes reveived
                                    Map<Long, StudioTableRow> tab4Players = new HashMap<>();
                                    List<Event> tab4ImageOrangeData = new ArrayList<>();

                                    eventTypeId = 34L;
                                    tagType = "416";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(null)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(null).get(eventTypeId).get(tagType)) {
                                            if (event.getPlayerIds() != null) {
                                                for (Long tmpPlayerId : event.getPlayerIds()) {
                                                    if (event.getPlayerToIds() != null && event.getPlayerToIds().contains(playerId)) {
                                                        Player tmpPlayer;
                                                        if (players.containsKey(tmpPlayerId)) {
                                                            tmpPlayer = players.get(tmpPlayerId);
                                                        } else if (playersMap.containsKey(tmpPlayerId)) {
                                                            tmpPlayer = playersMap.get(tmpPlayerId);
                                                        } else {
                                                            tmpPlayer = uService.getPlayer(tmpPlayerId, true);
                                                        }
                                                        if (tmpPlayer != null) {
                                                            tab4Players.putIfAbsent(tmpPlayerId, new StudioTableRow(tmpPlayer.getKnownName()));
                                                            playersMap.put(tmpPlayer.getId(), tmpPlayer);
                                                            if (fixturePlayersMap.containsKey(tmpPlayerId)) {
                                                                tab4Players.get(tmpPlayerId).setNumber(fixturePlayersMap.get(tmpPlayerId).getJerseyNumber());
                                                            }
                                                            if (tab4Players.get(tmpPlayerId).getValues().isEmpty()) {
                                                                tab4Players.get(tmpPlayerId).getValues().add(new StudioTableValue());
                                                            }

                                                            increaseTableValue(tab4Players.get(tmpPlayerId).getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                            tab4ImageOrangeData.add(event);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    for (Long tmpPlayerId : tab4Players.keySet()) {
                                        tab4Table.add(tab4Players.get(tmpPlayerId));
                                    }

                                    Collections.sort(tab4Table, new Comparator<StudioTableRow>() {
                                        @Override
                                        public int compare(StudioTableRow o1, StudioTableRow o2) {
                                            if (Long.compare(o1.getValues().get(0).getTotal(), o2.getValues().get(0).getTotal()) == 0) {
                                                if (Long.compare(o1.getValues().get(0).getFirstHalf(), o2.getValues().get(0).getFirstHalf()) == 0) {
                                                    if (Long.compare(o1.getValues().get(0).getSecondHalf(), o2.getValues().get(0).getSecondHalf()) == 0) {
                                                        return o1.getText().compareTo(o2.getText());
                                                    } else {
                                                        return o2.getValues().get(0).getSecondHalf().compareTo(o1.getValues().get(0).getSecondHalf());
                                                    }
                                                } else {
                                                    return o2.getValues().get(0).getFirstHalf().compareTo(o1.getValues().get(0).getFirstHalf());
                                                }
                                            } else {
                                                return o2.getValues().get(0).getTotal().compareTo(o1.getValues().get(0).getTotal());
                                            }
                                        }
                                    });
                                    if (tab4Table.size() > 15) {
                                        tab4Table = tab4Table.subList(0, 14);
                                    }

                                    String tab4Image = null;
                                    if (tab4Image == null) {
                                        tab4Image = MatchStudioHelper.drawFieldWithArrowByActionList(tab4ImageOrangeData, tab3ImageBlueData, fixturePlayer.getJerseyNumber().toString(), fixturePlayersMap);
                                    }
                                    playerDataModel.put("mTab4Image", tab4Image);

                                    // QUINTA TABELLA
                                    // Duels and dribbling
                                    List<StudioTableRow> tab5Table = new ArrayList<>();
                                    List<Event> tab5ImageData = new ArrayList<>();
                                    List<Event> tab5ImageOrangeData = new ArrayList<>();
                                    List<Event> tab5ImageBlueData = new ArrayList<>();
                                    List<Event> tab5ImageCyanData = new ArrayList<>();

                                    StudioTableRow duelsWon = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.duelli.vinti.lower", locale));
                                    duelsWon.getValues().add(new StudioTableValue());
                                    StudioTableRow duelsLost = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.duelli.persi.lower", locale));
                                    duelsLost.getValues().add(new StudioTableValue());
                                    StudioTableRow dribblingsWon = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.dribbling.vinti.lower", locale));
                                    dribblingsWon.getValues().add(new StudioTableValue());
                                    StudioTableRow dribblingsLost = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.dribbling.persi.lower", locale));
                                    dribblingsLost.getValues().add(new StudioTableValue());

                                    eventTypeId = 9L;
                                    tagType = null;
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(duelsWon.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                            tab5ImageData.add(event);
                                        }
                                    }
                                    eventTypeId = 23L;
                                    tagType = null;
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(duelsLost.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                            tab5ImageOrangeData.add(event);
                                        }
                                    }
                                    eventTypeId = 9L;
                                    tagType = "68";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(dribblingsWon.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                            tab5ImageBlueData.add(event);
                                        }
                                    }
                                    eventTypeId = 23L;
                                    tagType = "127";
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).containsKey(eventTypeId)
                                            && groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).containsKey(tagType)) {
                                        for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(playerId).get(eventTypeId).get(tagType)) {
                                            increaseTableValue(dribblingsLost.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                            tab5ImageCyanData.add(event);
                                        }
                                    }

                                    tab5Table.add(duelsWon);
                                    tab5Table.add(duelsLost);
                                    tab5Table.add(dribblingsWon);
                                    tab5Table.add(dribblingsLost);
                                    playerDataModel.put("mTab5Table", tab5Table);

                                    String tab5Image = null;
                                    if (tab5Image == null) {
                                        tab5Image = MatchStudioHelper.drawFieldDuelliDribblingByActionList(tab5ImageOrangeData, tab5ImageData, tab5ImageBlueData, tab5ImageCyanData);
                                    }
                                    playerDataModel.put("mTab5Image", tab5Image);

                                    // SESTA TABELLA
                                    // Ball Touches
                                    tab6Table = new ArrayList<>();
                                    StudioTableRow ownArea = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.propria.area", locale));
                                    ownArea.getValues().add(new StudioTableValue());
                                    StudioTableRow opponentArea = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.area.avversaria", locale));
                                    opponentArea.getValues().add(new StudioTableValue());
                                    StudioTableRow defensiveThird = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.terzo.difensivo", locale));
                                    defensiveThird.getValues().add(new StudioTableValue());
                                    StudioTableRow centralThird = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.terzo.centrale", locale));
                                    centralThird.getValues().add(new StudioTableValue());
                                    StudioTableRow offensiveThird = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.terzo.offensivo", locale));
                                    offensiveThird.getValues().add(new StudioTableValue());
                                    StudioTableRow channelOne = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.canale.1", locale));
                                    channelOne.getValues().add(new StudioTableValue());
                                    StudioTableRow channelTwo = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.canale.2", locale));
                                    channelTwo.getValues().add(new StudioTableValue());
                                    StudioTableRow channelThree = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.canale.3", locale));
                                    channelThree.getValues().add(new StudioTableValue());
                                    StudioTableRow channelFour = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.canale.4", locale));
                                    channelFour.getValues().add(new StudioTableValue());
                                    StudioTableRow channelFive = new StudioTableRow(SpringApplicationContextHelper.getMessage("match.studio.canale.5", locale));
                                    channelFive.getValues().add(new StudioTableValue());
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(playerId)) {
                                        for (Event event : getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId))) {
                                            if (BooleanUtils.isTrue(event.getInArea())) {
                                                if (event.getThird() != null) {
                                                    if (event.getThird() == 1) {
                                                        // own area
                                                        increaseTableValue(ownArea.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    } else if (event.getThird() == 3) {
                                                        // opponent area
                                                        increaseTableValue(opponentArea.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                    }
                                                }
                                            }
                                            if (event.getThird() != null) {
                                                switch (event.getThird()) {
                                                    case 1:
                                                        // defensive third
                                                        increaseTableValue(defensiveThird.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 2:
                                                        // central third
                                                        increaseTableValue(centralThird.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 3:
                                                        // offensive third
                                                        increaseTableValue(offensiveThird.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    default:
                                                        break;
                                                }
                                            }
                                            if (event.getChannel() != null) {
                                                switch (event.getChannel()) {
                                                    case 1:
                                                        // channel 1
                                                        increaseTableValue(channelOne.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 2:
                                                        // channel 2
                                                        increaseTableValue(channelTwo.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 3:
                                                        // channel 3
                                                        increaseTableValue(channelThree.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 4:
                                                        // channel 4
                                                        increaseTableValue(channelFour.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    case 5:
                                                        // channel 5
                                                        increaseTableValue(channelFive.getValues().get(0), event, fixtureDetail, fixturePlayer);
                                                        break;
                                                    default:
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                    tab6Table.add(ownArea);
                                    tab6Table.add(opponentArea);
                                    tab6Table.add(defensiveThird);
                                    tab6Table.add(centralThird);
                                    tab6Table.add(offensiveThird);
                                    tab6Table.add(channelOne);
                                    tab6Table.add(channelTwo);
                                    tab6Table.add(channelThree);
                                    tab6Table.add(channelFour);
                                    tab6Table.add(channelFive);

                                    String tab6Image = null;
                                    if (tab6Image == null) {
                                        tab6Image = MatchStudioHelper.drawFieldPointsByActionList(getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId)));
                                    }
                                    playerDataModel.put("mTab6Image", tab6Image);

                                    // SETTIMA TABELLA
                                    List<Event> tmpEventList = new ArrayList<>();
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(fixturePlayer.getPlayerId())) {
                                        tmpEventList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId));
                                    }
                                    MatchStudioHeatMap myMap1 = MatchStudioHelper.buildHeatMapFromActionList(tmpEventList);
                                    BufferedImage heatMapT1 = myMap1.createHeatMap(0.9f);
                                    heatMapT1 = MatchStudioHelper.rotateImageBy(heatMapT1, 90);
                                    try {
                                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                        ImageIO.write(heatMapT1, "png", baos);
                                        String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                                        playerDataModel.put("mTab7Image", base64Image);
                                    } catch (IOException ex) {
                                        GlobalHelper.reportError(ex);
                                    }

                                    // OTTAVA TABELLA
                                    JsonArray jsonArray = new JsonArray();
                                    int halfDuration = MatchStudioHelper.getHalfDuration(fixtureDetail);
                                    int halfInterval = halfDuration / 3;

                                    tmpEventList = new ArrayList<>();
                                    List<Event> tmpPlayerEventList = new ArrayList<>();
                                    for (FixturePlayer tmpFixturePlayer : fixturePlayers) {
                                        if (tmpFixturePlayer.getTeamId() != null && Long.compare(tmpFixturePlayer.getTeamId(), fixturePlayer.getTeamId()) == 0) {
                                            if (tmpFixturePlayer.getPositionId() != null && tmpFixturePlayer.getPositionId() != 1) { // niente portieri
                                                if (groupedEvents.containsKey(tmpFixturePlayer.getTeamId()) && groupedEvents.get(tmpFixturePlayer.getTeamId()).containsKey(tmpFixturePlayer.getPlayerId())) {
                                                    tmpEventList.addAll(getPlayerTouches(groupedEvents.get(tmpFixturePlayer.getTeamId()).get(tmpFixturePlayer.getPlayerId())));
                                                }
                                            }
                                        }
                                    }
                                    if (groupedEvents.containsKey(fixturePlayer.getTeamId()) && groupedEvents.get(fixturePlayer.getTeamId()).containsKey(fixturePlayer.getPlayerId())) {
                                        tmpPlayerEventList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(playerId));
                                    }

                                    // calcolo primo e secondo tempo
                                    for (int i = 1; i <= 6; i++) {
                                        long maxStartAzione, minStartAzione = 0;
                                        List<Long> validPlayers = new ArrayList<>();
                                        if (i <= 3) {
                                            if (i == 3) {
                                                maxStartAzione = fixtureDetail.getEndTime1();
                                            } else {
                                                maxStartAzione = fixtureDetail.getStartTime1() + ((halfInterval * 60 * 1000) * i);
                                            }
                                            if (i > 0) {
                                                minStartAzione = fixtureDetail.getStartTime1() + ((halfInterval * 60 * 1000) * (i - 1));
                                            }
                                        } else {
                                            if (i == 6) {
                                                maxStartAzione = fixtureDetail.getEndTime2();
                                            } else {
                                                maxStartAzione = fixtureDetail.getStartTime2() + ((halfInterval * 60 * 1000) * (i - 3));
                                            }
                                            minStartAzione = fixtureDetail.getStartTime2() + ((halfInterval * 60 * 1000) * (i - 4));
                                        }

                                        int totalTeam = 0, totalPlayer = 0;
                                        for (Event tmpEvent : tmpEventList) {
                                            if (tmpEvent.getStartMillis() != null && tmpEvent.getEndMillis() != null) {
                                                if (tmpEvent.getStartMillis() >= minStartAzione && tmpEvent.getStartMillis() <= maxStartAzione) {
                                                    totalTeam++;
                                                    if (tmpEvent.getPlayerIds() != null) {
                                                        for (Long tmpPlayerId : tmpEvent.getPlayerIds()) {
                                                            if (!validPlayers.contains(tmpPlayerId)) {
                                                                validPlayers.add(tmpPlayerId);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        for (Event tmpEvent : tmpPlayerEventList) {
                                            if (tmpEvent.getStartMillis() != null && tmpEvent.getEndMillis() != null) {
                                                if (tmpEvent.getStartMillis() >= minStartAzione && tmpEvent.getStartMillis() <= maxStartAzione) {
                                                    totalPlayer++;
                                                }
                                            }
                                        }

                                        JsonObject element = new JsonObject();
                                        element.addProperty("year", halfInterval * i);
                                        if (!validPlayers.isEmpty()) {
                                            element.addProperty("value", Math.round(1D * totalTeam / validPlayers.size()));
                                        }
                                        if (totalPlayer > 0) {
                                            element.addProperty("value2", totalPlayer);
                                        }

                                        jsonArray.add(element);
                                    }

                                    playerDataModel.put("mTab8Data", jsonArray.toString());
                                }
                            }

                            playerDataModel.put("mTab1Table", tab1Table);
                            playerDataModel.put("mTab3Table", tab3Table);
                            playerDataModel.put("mTab4Table", tab4Table);
                            playerDataModel.put("mTab6Table", tab6Table);
                            playerDataModel.put("mPlayer", playersMap.get(playerId));
                            playerDataModel.put("mFixturePlayer", fixturePlayersMap.get(playerId));

                            playerDataMap.put(playerId, playerDataModel);
                        }
                    }
                }

                model.addAttribute("mGroupedEvents", groupedEvents);
                model.addAttribute("mGroupedAdvancedMetrics", groupedAdvancedMetrics);
                model.addAttribute("mCompetition", competition);
                model.addAttribute("mFixture", fixture);
                model.addAttribute("mFixtureDetails", fixtureDetail);
                model.addAttribute("mPlayerIds", playerIds);
                model.addAttribute("mPlayers", playersMap);
                model.addAttribute("mPlayerDataMap", playerDataMap);
                model.addAttribute("mFixturePlayers", fixturePlayersMap);
                model.addAttribute("mTeams", fixtureTeamsMap);
                model.addAttribute("mPositions", positions);
            }
        }

        return pageMatchStudio;
    }

    private static void populatePasses(Map<Long, Map<Long, Map<Long, Map<String, List<Event>>>>> groupedEvents, Map<Long, Player> playersMap, FixtureDetails fixtureDetail, ModelMap model, List<FixturePlayer> homeTeamPlayers, List<FixturePlayer> awayTeamPlayers) {
        // POSIZIONI MEDIE
        BufferedImage fieldPosMedia = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        BufferedImage fieldPosMediaOrizzontale = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        BufferedImage fieldPosMedia1T = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        BufferedImage fieldPosMedia2T = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);

        Map<String, Map<String, Integer>> passaggiRaggruppati = new HashMap<>();
        Map<String, Point> posizioneMediaGiocatori = new HashMap<>();
        Map<String, String> giocatoriNomeNumero = new HashMap<>();
        for (FixturePlayer fixturePlayer : homeTeamPlayers) {
            Player player = playersMap.get(fixturePlayer.getPlayerId());
            giocatoriNomeNumero.put(player.getKnownName() + "-1", fixturePlayer.getJerseyNumber().toString());

            List<Event> tmpActionList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()));
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(20L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(20L).containsKey(null)) {
                tmpActionList.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(20L).get(null));
            }
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(100L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(100L).containsKey(null)) {
                tmpActionList.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(100L).get(null));
            }

            List<Point> pointList = new ArrayList<>();
            List<Point> puntiPrimoTempo = new ArrayList<>();
            List<Point> puntiSecondoTempo = new ArrayList<>();

            for (Event event : tmpActionList) {
                double posX = -1;
                double posY = -1;
                boolean isPrimoTempo = false;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = MatchStudioHelper.moltCampo * event.getStartPointNormalized().getY();
                    posY = MatchStudioHelper.moltCampo * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                if (event.getPeriod() == 1) {
                    isPrimoTempo = true;
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point punto = MatchStudioHelper.scalaSuFieldBig(posX, posY);
                    MatchStudioHelper.centraPalliniTagliati(fieldPosMedia, punto, 20);

                    pointList.add(punto);
                    if (isPrimoTempo) {
                        puntiPrimoTempo.add(punto);
                    } else {
                        puntiSecondoTempo.add(punto);
                    }
                }
            }
            // PUNTI TOTALI
            int totalX = 0, totalY = 0;
            for (Point point : pointList) {
                totalX += point.getX();
                totalY += point.getY();
            }

            double averageX = 1D * totalX / pointList.size();
            double averageY = 1D * totalY / pointList.size();
            Point punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            List<Event> sostituzioni = new ArrayList<>();
            List<Event> subentri = new ArrayList<>();
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(29L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(29L).containsKey(null)) {
                sostituzioni.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(29L).get(null));
            }
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(30L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(30L).containsKey(null)) {
                subentri.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(30L).get(null));
            }

            if (averageX > 0 && averageY > 0) {
                posizioneMediaGiocatori.put(player.getKnownName() + "-1", punto);
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia, player, fixturePlayer, punto, 10, fixtureDetail.getHomeColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getHomeColor()), sostituzioni, subentri, false);
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMediaOrizzontale, player, fixturePlayer, punto, 10, fixtureDetail.getHomeColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getHomeColor()), sostituzioni, subentri, true);
            }

            // PUNTI PRIMO TEMPO
            totalX = 0;
            totalY = 0;
            for (Point point : puntiPrimoTempo) {
                totalX += point.getX();
                totalY += point.getY();
            }

            averageX = 1D * totalX / puntiPrimoTempo.size();
            averageY = 1D * totalY / puntiPrimoTempo.size();
            punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            if (averageX > 0 && averageY > 0) {
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia1T, player, fixturePlayer, punto, 10, fixtureDetail.getHomeColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getHomeColor()), sostituzioni, subentri, false);
            }

            // PUNTI SECONDO TEMPO
            totalX = 0;
            totalY = 0;
            for (Point point : puntiSecondoTempo) {
                totalX += point.getX();
                totalY += point.getY();
            }

            averageX = 1D * totalX / puntiSecondoTempo.size();
            averageY = 1D * totalY / puntiSecondoTempo.size();
            punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            if (averageX > 0 && averageY > 0) {
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia2T, player, fixturePlayer, punto, 10, fixtureDetail.getHomeColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getHomeColor()), sostituzioni, subentri, false);
            }

            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(34L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(34L).containsKey("416")) {
                for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(34L).get("416")) {
                    if (event.getPlayerToIdList() != null) {
                        if (passaggiRaggruppati.get(player.getKnownName() + "-1") == null) {
                            passaggiRaggruppati.put(player.getKnownName() + "-1", new HashMap<String, Integer>());
                        }
                        String playerToName = null;
                        if (StringUtils.isNumeric(event.getPlayerToIdList())) {
                            playerToName = playersMap.get(Long.valueOf(event.getPlayerToIdList())).getKnownName();
                        }
                        if (passaggiRaggruppati.get(player.getKnownName() + "-1").get(playerToName + "-1") == null) {
                            passaggiRaggruppati.get(player.getKnownName() + "-1").put(playerToName + "-1", 0);
                        }
                        passaggiRaggruppati.get(player.getKnownName() + "-1").put(playerToName + "-1", passaggiRaggruppati.get(player.getKnownName() + "-1").get(playerToName + "-1") + 1);
                    }
                }
            }
        }

        int maxValue = 0;
        String chartDatas = "[";
        List<String> allPassesPlayers = new ArrayList<>(passaggiRaggruppati.keySet());
        List<String> allPassesPlayersSorted = new ArrayList<>();

        // ordino ora per lo stesso ordinamento impostato nella prima pagina del report
        // ovvero per ruolo e per numero di maglia crescente
        // per i subentri, sono ordinati per minuto in cui sono entrati
        for (FixturePlayer fixturePlayer : homeTeamPlayers) {
            for (String playerName : allPassesPlayers) {
                if (StringUtils.equalsIgnoreCase(playersMap.get(fixturePlayer.getPlayerId()).getKnownName() + "-1", playerName)) {
                    allPassesPlayersSorted.add(playerName);
                    break;
                }
            }
        }

        // nel caso in cui ci sono record in meno (non so se può succedere)
        // aggiungo alla fine quelli che mancano
        if (allPassesPlayersSorted.size() != allPassesPlayers.size()) {
            for (String playerName : allPassesPlayers) {
                if (!allPassesPlayersSorted.contains(playerName)) {
                    allPassesPlayersSorted.add(playerName);
                }
            }
        }

        Collections.reverse(allPassesPlayersSorted);
        for (String player : allPassesPlayersSorted) {
            String baseValue = "{\n"
                    + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                    + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                    + "    \"player\": \"" + giocatoriNomeNumero.get(player) + "\",\n"
                    + "    \"playerName\": \"" + player.replace("-1", "") + "\",\n"
                    + "    \"value\": -1\n"
                    + "  },";

            chartDatas += baseValue;
            Map<String, Integer> playerPasses = passaggiRaggruppati.get(player);
            for (String playerTo : allPassesPlayersSorted) {
                if (!player.equals(playerTo)) {
                    String value;
                    if (playerPasses.get(playerTo) != null) {
                        value = "{\n"
                                + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                                + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                                + "    \"player\": \"" + giocatoriNomeNumero.get(playerTo) + "\",\n"
                                + "    \"playerName\": \"" + playerTo.replace("-1", "") + "\",\n"
                                + "    \"value\": " + playerPasses.get(playerTo) + "\n"
                                + "  },";

                        if (playerPasses.get(playerTo) > maxValue) {
                            maxValue = playerPasses.get(playerTo);
                        }
                    } else {
                        value = "{\n"
                                + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                                + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                                + "    \"player\": \"" + giocatoriNomeNumero.get(playerTo) + "\",\n"
                                + "    \"playerName\": \"" + playerTo.replace("-1", "") + "\",\n"
                                + "    \"value\": 0\n"
                                + "  },";
                    }

                    if (value != null) {
                        chartDatas += value;
                    }
                }
            }
        }

        chartDatas += "]";

        if (fieldPosMedia != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam1", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
        if (fieldPosMedia1T != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia1T, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam11T", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
        if (fieldPosMedia2T != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia2T, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam12T", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        model.addAttribute("mHeatMapPassesT1Data", chartDatas);
        model.addAttribute("mHeatMapPassesT1Width", 500);
        model.addAttribute("mHeatMapPassesT1Height", 500);
        model.addAttribute("mHeatMapPassesT1MaxValue", maxValue);

        // Ora dalla mappa di tutti i giocatori tiro fuori il giocatore con cui ogni giocatore ha fatto più passaggi
        List<PassesBean> passaggiConValoreMassimo = new ArrayList<>();
        for (String tmpPlayer : passaggiRaggruppati.keySet()) {
            Map<String, Integer> values = passaggiRaggruppati.get(tmpPlayer);
            maxValue = 0;
            String playerTo = null;

            for (String tmpPlayerTo : values.keySet()) {
                if (values.get(tmpPlayerTo) > maxValue) {
                    maxValue = values.get(tmpPlayerTo);
                    playerTo = tmpPlayerTo;
                }
            }

            PassesBean passaggi = new PassesBean();
            passaggi.setPlayer(tmpPlayer);
            passaggi.setPlayerTo(playerTo);
            passaggi.setMax(maxValue);

            passaggiConValoreMassimo.add(passaggi);
        }

        Collections.sort(passaggiConValoreMassimo, new Comparator<PassesBean>() {
            @Override
            public int compare(PassesBean o1, PassesBean o2) {
                return o2.getMax().compareTo(o1.getMax());
            }
        });

        // se ne ho più di 7 prendo solo i primi 7 altrimenti ci sono troppe freccie
        if (passaggiConValoreMassimo.size() > 7) {
            passaggiConValoreMassimo = passaggiConValoreMassimo.subList(0, 7);
        }

        Map<Point, Point> mostPassesMap = new LinkedHashMap<>();
        for (PassesBean bean : passaggiConValoreMassimo) {
            mostPassesMap.put(posizioneMediaGiocatori.get(bean.getPlayer()), posizioneMediaGiocatori.get(bean.getPlayerTo()));
        }

        MatchStudioHelper.drawArrowByPointList(fieldPosMediaOrizzontale, mostPassesMap);
        fieldPosMediaOrizzontale = MatchStudioHelper.rotateImageBy(fieldPosMediaOrizzontale, 90);
        if (fieldPosMediaOrizzontale != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMediaOrizzontale, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam1Orizzontale", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        fieldPosMedia = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        fieldPosMediaOrizzontale = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        fieldPosMedia1T = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);
        fieldPosMedia2T = MatchStudioHelper.drawEmptyVerticalField(EventHelper.kLatoCortoCampo * MatchStudioHelper.moltCampo, EventHelper.kLatoLungoCampo * MatchStudioHelper.moltCampo);

        passaggiRaggruppati = new HashMap<>();
        posizioneMediaGiocatori = new HashMap<>();
        giocatoriNomeNumero = new HashMap<>();
        for (FixturePlayer fixturePlayer : awayTeamPlayers) {
            Player player = playersMap.get(fixturePlayer.getPlayerId());
            giocatoriNomeNumero.put(player.getKnownName() + "-1", fixturePlayer.getJerseyNumber().toString());

            List<Event> tmpActionList = getPlayerTouches(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()));
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(20L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(20L).containsKey(null)) {
                tmpActionList.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(20L).get(null));
            }
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(100L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(100L).containsKey(null)) {
                tmpActionList.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(100L).get(null));
            }

            List<Point> pointList = new ArrayList<>();
            List<Point> puntiPrimoTempo = new ArrayList<>();
            List<Point> puntiSecondoTempo = new ArrayList<>();

            for (Event event : tmpActionList) {
                double posX = -1;
                double posY = -1;
                boolean isPrimoTempo = false;
                if (!event.getStartPointNormalized().getIsDefault()) {
                    posX = MatchStudioHelper.moltCampo * event.getStartPointNormalized().getY();
                    posY = MatchStudioHelper.moltCampo * (EventHelper.kLatoLungoCampo - event.getStartPointNormalized().getX());
                }
                if (event.getPeriod() == 1) {
                    isPrimoTempo = true;
                }
                //disegno i pallini e i numeri sui campi
                if (posX >= 0 && posY >= 0) {
                    Point punto = MatchStudioHelper.scalaSuFieldBig(posX, posY);
                    MatchStudioHelper.centraPalliniTagliati(fieldPosMedia, punto, 20);

                    pointList.add(punto);
                    if (isPrimoTempo) {
                        puntiPrimoTempo.add(punto);
                    } else {
                        puntiSecondoTempo.add(punto);
                    }
                }
            }
            // PUNTI TOTALI
            int totalX = 0, totalY = 0;
            for (Point point : pointList) {
                totalX += point.getX();
                totalY += point.getY();
            }

            double averageX = 1D * totalX / pointList.size();
            double averageY = 1D * totalY / pointList.size();
            Point punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            List<Event> sostituzioni = new ArrayList<>();
            List<Event> subentri = new ArrayList<>();
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(29L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(29L).containsKey(null)) {
                sostituzioni.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(29L).get(null));
            }
            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(30L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(30L).containsKey(null)) {
                subentri.addAll(groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(30L).get(null));
            }

            if (averageX > 0 && averageY > 0) {
                posizioneMediaGiocatori.put(player.getKnownName() + "-1", punto);
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia, player, fixturePlayer, punto, 10, fixtureDetail.getAwayColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getAwayColor()), sostituzioni, subentri, false);
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMediaOrizzontale, player, fixturePlayer, punto, 10, fixtureDetail.getAwayColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getAwayColor()), sostituzioni, subentri, true);
            }

            // PUNTI PRIMO TEMPO
            totalX = 0;
            totalY = 0;
            for (Point point : puntiPrimoTempo) {
                totalX += point.getX();
                totalY += point.getY();
            }

            averageX = 1D * totalX / puntiPrimoTempo.size();
            averageY = 1D * totalY / puntiPrimoTempo.size();
            punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            if (averageX > 0 && averageY > 0) {
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia1T, player, fixturePlayer, punto, 10, fixtureDetail.getAwayColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getAwayColor()), sostituzioni, subentri, false);
            }

            // PUNTI SECONDO TEMPO
            totalX = 0;
            totalY = 0;
            for (Point point : puntiSecondoTempo) {
                totalX += point.getX();
                totalY += point.getY();
            }

            averageX = 1D * totalX / puntiSecondoTempo.size();
            averageY = 1D * totalY / puntiSecondoTempo.size();
            punto = new Point();
            punto.setX(averageX);
            punto.setY(averageY);

            if (averageX > 0 && averageY > 0) {
                MatchStudioHelper.drawPallinoPosizioneMedia(fieldPosMedia2T, player, fixturePlayer, punto, 10, fixtureDetail.getAwayColor(), MatchStudioHelper.getColoreInContrasto(fixtureDetail.getAwayColor()), sostituzioni, subentri, false);
            }

            if (groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).containsKey(34L)
                    && groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(34L).containsKey("416")) {
                for (Event event : groupedEvents.get(fixturePlayer.getTeamId()).get(fixturePlayer.getPlayerId()).get(34L).get("416")) {
                    if (event.getPlayerToIdList() != null) {
                        if (passaggiRaggruppati.get(player.getKnownName() + "-1") == null) {
                            passaggiRaggruppati.put(player.getKnownName() + "-1", new HashMap<String, Integer>());
                        }
                        String playerToName = null;
                        if (StringUtils.isNumeric(event.getPlayerToIdList())) {
                            playerToName = playersMap.get(Long.valueOf(event.getPlayerToIdList())).getKnownName();
                        }
                        if (passaggiRaggruppati.get(player.getKnownName() + "-1").get(playerToName + "-1") == null) {
                            passaggiRaggruppati.get(player.getKnownName() + "-1").put(playerToName + "-1", 0);
                        }
                        passaggiRaggruppati.get(player.getKnownName() + "-1").put(playerToName + "-1", passaggiRaggruppati.get(player.getKnownName() + "-1").get(playerToName + "-1") + 1);
                    }
                }
            }
        }

        maxValue = 0;
        chartDatas = "[";
        allPassesPlayers = new ArrayList<>(passaggiRaggruppati.keySet());
        allPassesPlayersSorted = new ArrayList<>();

        // ordino ora per lo stesso ordinamento impostato nella prima pagina del report
        // ovvero per ruolo e per numero di maglia crescente
        // per i subentri, sono ordinati per minuto in cui sono entrati
        for (FixturePlayer fixturePlayer : awayTeamPlayers) {
            for (String playerName : allPassesPlayers) {
                if (StringUtils.equalsIgnoreCase(playersMap.get(fixturePlayer.getPlayerId()).getKnownName() + "-1", playerName)) {
                    allPassesPlayersSorted.add(playerName);
                    break;
                }
            }
        }

        // nel caso in cui ci sono record in meno (non so se può succedere)
        // aggiungo alla fine quelli che mancano
        if (allPassesPlayersSorted.size() != allPassesPlayers.size()) {
            for (String playerName : allPassesPlayers) {
                if (!allPassesPlayersSorted.contains(playerName)) {
                    allPassesPlayersSorted.add(playerName);
                }
            }
        }

        Collections.reverse(allPassesPlayersSorted);
        for (String player : allPassesPlayersSorted) {
            String baseValue = "{\n"
                    + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                    + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                    + "    \"player\": \"" + giocatoriNomeNumero.get(player) + "\",\n"
                    + "    \"playerName\": \"" + player.replace("-1", "") + "\",\n"
                    + "    \"value\": -1\n"
                    + "  },";

            chartDatas += baseValue;
            Map<String, Integer> playerPasses = passaggiRaggruppati.get(player);
            for (String playerTo : allPassesPlayersSorted) {
                if (!player.equals(playerTo)) {
                    String value;
                    if (playerPasses.get(playerTo) != null) {
                        value = "{\n"
                                + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                                + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                                + "    \"player\": \"" + giocatoriNomeNumero.get(playerTo) + "\",\n"
                                + "    \"playerName\": \"" + playerTo.replace("-1", "") + "\",\n"
                                + "    \"value\": " + playerPasses.get(playerTo) + "\n"
                                + "  },";

                        if (playerPasses.get(playerTo) > maxValue) {
                            maxValue = playerPasses.get(playerTo);
                        }
                    } else {
                        value = "{\n"
                                + "    \"playerTo\": \"" + player.replace("-1", "") + " - " + giocatoriNomeNumero.get(player) + "\",\n"
                                + "    \"playerToName\": \"" + player.replace("-1", "") + "\",\n"
                                + "    \"player\": \"" + giocatoriNomeNumero.get(playerTo) + "\",\n"
                                + "    \"playerName\": \"" + playerTo.replace("-1", "") + "\",\n"
                                + "    \"value\": 0\n"
                                + "  },";
                    }

                    if (value != null) {
                        chartDatas += value;
                    }
                }
            }
        }

        chartDatas += "]";

        if (fieldPosMedia != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam2", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
        if (fieldPosMedia1T != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia1T, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam21T", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
        if (fieldPosMedia2T != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMedia2T, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam22T", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }

        model.addAttribute("mHeatMapPassesT2Data", chartDatas);
        model.addAttribute("mHeatMapPassesT2Width", 500);
        model.addAttribute("mHeatMapPassesT2Height", 500);
        model.addAttribute("mHeatMapPassesT2MaxValue", maxValue);

        // Ora dalla mappa di tutti i giocatori tiro fuori il giocatore con cui ogni giocatore ha fatto più passaggi
        passaggiConValoreMassimo = new ArrayList<>();
        for (String tmpPlayer : passaggiRaggruppati.keySet()) {
            Map<String, Integer> values = passaggiRaggruppati.get(tmpPlayer);
            maxValue = 0;
            String playerTo = null;

            for (String tmpPlayerTo : values.keySet()) {
                if (values.get(tmpPlayerTo) > maxValue) {
                    maxValue = values.get(tmpPlayerTo);
                    playerTo = tmpPlayerTo;
                }
            }

            PassesBean passaggi = new PassesBean();
            passaggi.setPlayer(tmpPlayer);
            passaggi.setPlayerTo(playerTo);
            passaggi.setMax(maxValue);

            passaggiConValoreMassimo.add(passaggi);
        }

        Collections.sort(passaggiConValoreMassimo, new Comparator<PassesBean>() {
            @Override
            public int compare(PassesBean o1, PassesBean o2) {
                return o2.getMax().compareTo(o1.getMax());
            }
        });

        // se ne ho più di 7 prendo solo i primi 7 altrimenti ci sono troppe freccie
        if (passaggiConValoreMassimo.size() > 7) {
            passaggiConValoreMassimo = passaggiConValoreMassimo.subList(0, 7);
        }

        mostPassesMap = new LinkedHashMap<>();
        for (PassesBean bean : passaggiConValoreMassimo) {
            mostPassesMap.put(posizioneMediaGiocatori.get(bean.getPlayer()), posizioneMediaGiocatori.get(bean.getPlayerTo()));
        }

        MatchStudioHelper.drawArrowByPointList(fieldPosMediaOrizzontale, mostPassesMap);
        fieldPosMediaOrizzontale = MatchStudioHelper.rotateImageBy(fieldPosMediaOrizzontale, 90);
        if (fieldPosMediaOrizzontale != null) {
            try {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(fieldPosMediaOrizzontale, "png", baos);
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                model.addAttribute("mPosizioneMediaTeam2Orizzontale", base64Image);
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
    }

    public static void populateAnalysis(Map<Long, Map<Long, Map<Long, Map<String, List<Event>>>>> groupedEvents, Map<Long, Player> playersMap, FixtureDetails fixtureDetail, ModelMap model, Fixture fixture, Map<Long, FixturePlayer> fixturePlayersMap, Locale locale) {
        // ANALISI EVENTI - TEAM 1
        List<Event> tmpEventList = new ArrayList();
        long eventTypeId = 19L;
        String tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisFoulsT1", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 24L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisFoulsConcededT1", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 10L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisPossessionLostT1", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 11L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisBallRecoveryT1", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 9L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        eventTypeId = 23L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList.addAll(groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType));
        }
        model.addAttribute("mEventsAnalysisDuelsT1", MatchStudioHelper.drawFieldDuelli(tmpEventList));

        tmpEventList = new ArrayList();
        eventTypeId = 8L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisDribblingsT1", MatchStudioHelper.drawFieldDribbling(tmpEventList, fixturePlayersMap));

        // ANALISI EVENTI - TEAM 2
        tmpEventList = new ArrayList();
        eventTypeId = 19L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisFoulsT2", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 24L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisFoulsConcededT2", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 10L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisPossessionLostT2", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 11L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisBallRecoveryT2", MatchStudioHelper.drawField(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 9L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        eventTypeId = 23L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList.addAll(groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType));
        }
        model.addAttribute("mEventsAnalysisDuelsT2", MatchStudioHelper.drawFieldDuelli(tmpEventList));

        tmpEventList = new ArrayList();
        eventTypeId = 8L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisDribblingsT2", MatchStudioHelper.drawFieldDribbling(tmpEventList, fixturePlayersMap));

        // ANALISI PASSAGGI CHIAVE - TEAM 1
        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisKillerPassesT1", MatchStudioHelper.drawFieldPassaggiChiave(tmpEventList, fixturePlayersMap));

        // RANKING PASSAGGI CHIAVE RICEVUTI
        List<PlayerRanking> tmpPassaggiChiaveRankingList = new ArrayList();
        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            Map<Long, Integer> tmpPassaggiChiaveRankingMap = new HashMap();
            for (Event event : groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getPlayerToIds() != null && !event.getPlayerToIds().isEmpty()) {
                    for (Long playerId : event.getPlayerToIds()) {
                        tmpPassaggiChiaveRankingMap.putIfAbsent(playerId, 0);
                        tmpPassaggiChiaveRankingMap.put(playerId, tmpPassaggiChiaveRankingMap.get(playerId) + 1);
                    }
                }
            }

            // sort by value
            // sort by the counter (value of the map)
            List<Map.Entry<Long, Integer>> sortedEntries = new ArrayList<Map.Entry<Long, Integer>>(tmpPassaggiChiaveRankingMap.entrySet());
            Collections.sort(sortedEntries, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> e1, Map.Entry<Long, Integer> e2) {
                    if (e2.getValue().equals(e1.getValue())) {
                        return e1.getKey().compareTo(e2.getKey());
                    } else {
                        return e2.getValue().compareTo(e1.getValue()); // decrescente
                    }
                }
            });

            for (Map.Entry<Long, Integer> entry : sortedEntries) {
                Player tmpPlayer;
                Long playerId = entry.getKey();
                if (players.containsKey(playerId)) {
                    tmpPlayer = players.get(playerId);
                } else if (playersMap.containsKey(playerId)) {
                    tmpPlayer = playersMap.get(playerId);
                } else {
                    tmpPlayer = uService.getPlayer(playerId, true);
                }
                if (tmpPlayer != null) {
                    PlayerRanking playerRanking = new PlayerRanking();
                    playerRanking.setId(tmpPlayer.getId());
                    playerRanking.setFirstName(tmpPlayer.getFirstName());
                    playerRanking.setLastName(tmpPlayer.getLastName());
                    playerRanking.setKnownName(tmpPlayer.getKnownName());
                    playerRanking.setPhoto(tmpPlayer.getPhoto());
                    playerRanking.setTeamId(fixture.getHomeTeamId());
                    playerRanking.setCounter(entry.getValue());

                    tmpPassaggiChiaveRankingList.add(playerRanking);
                }
            }
        }
        model.addAttribute("mEventsAnalysisReceivedKillerPassesRankingT1", tmpPassaggiChiaveRankingList);

        tmpEventList = new ArrayList();
        eventTypeId = 100L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            for (Event event : groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(2400L)) {
                    continue;
                }
                tmpEventList.add(event);
            }
        }
        model.addAttribute("mEventsAnalysisSideBallsT1", MatchStudioHelper.drawFieldPalleLaterali(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        List<Event> killerPassAssist = new ArrayList();
        eventTypeId = 12L;
        tagType = "75";
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            killerPassAssist = groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        List<Event> sideBalls = new ArrayList();
        eventTypeId = 100L;
        tagType = null;
        if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            for (Event event : groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(2400L)) {
                    continue;
                }
                sideBalls.add(event);
            }
        }
        model.addAttribute("mEventsAnalysisTimeLineT1", MatchStudioHelper.drawPassaggiTimeline(fixtureDetail, tmpEventList, killerPassAssist, sideBalls, locale));

        // ANALISI PASSAGGI CHIAVE - TEAM 2
        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        model.addAttribute("mEventsAnalysisKillerPassesT2", MatchStudioHelper.drawFieldPassaggiChiave(tmpEventList, fixturePlayersMap));

        // RANKING PASSAGGI CHIAVE RICEVUTI
        tmpPassaggiChiaveRankingList = new ArrayList();
        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            Map<Long, Integer> tmpPassaggiChiaveRankingMap = new HashMap();
            for (Event event : groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getPlayerToIds() != null && !event.getPlayerToIds().isEmpty()) {
                    for (Long playerId : event.getPlayerToIds()) {
                        tmpPassaggiChiaveRankingMap.putIfAbsent(playerId, 0);
                        tmpPassaggiChiaveRankingMap.put(playerId, tmpPassaggiChiaveRankingMap.get(playerId) + 1);
                    }
                }
            }

            // sort by value
            // sort by the counter (value of the map)
            List<Map.Entry<Long, Integer>> sortedEntries = new ArrayList<Map.Entry<Long, Integer>>(tmpPassaggiChiaveRankingMap.entrySet());
            Collections.sort(sortedEntries, new Comparator<Map.Entry<Long, Integer>>() {
                @Override
                public int compare(Map.Entry<Long, Integer> e1, Map.Entry<Long, Integer> e2) {
                    if (e2.getValue().equals(e1.getValue())) {
                        return e1.getKey().compareTo(e2.getKey());
                    } else {
                        return e2.getValue().compareTo(e1.getValue()); // decrescente
                    }
                }
            });

            for (Map.Entry<Long, Integer> entry : sortedEntries) {
                Player tmpPlayer;
                Long playerId = entry.getKey();
                if (players.containsKey(playerId)) {
                    tmpPlayer = players.get(playerId);
                } else if (playersMap.containsKey(playerId)) {
                    tmpPlayer = playersMap.get(playerId);
                } else {
                    tmpPlayer = uService.getPlayer(playerId, true);
                }
                if (tmpPlayer != null) {
                    PlayerRanking playerRanking = new PlayerRanking();
                    playerRanking.setId(tmpPlayer.getId());
                    playerRanking.setFirstName(tmpPlayer.getFirstName());
                    playerRanking.setLastName(tmpPlayer.getLastName());
                    playerRanking.setKnownName(tmpPlayer.getKnownName());
                    playerRanking.setPhoto(tmpPlayer.getPhoto());
                    playerRanking.setTeamId(fixture.getAwayTeamId());
                    playerRanking.setCounter(entry.getValue());

                    tmpPassaggiChiaveRankingList.add(playerRanking);
                }
            }
        }
        model.addAttribute("mEventsAnalysisReceivedKillerPassesRankingT2", tmpPassaggiChiaveRankingList);

        tmpEventList = new ArrayList();
        eventTypeId = 100L;
        tagType = null;
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            for (Event event : groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(2400L)) {
                    continue;
                }
                tmpEventList.add(event);
            }
        }
        model.addAttribute("mEventsAnalysisSideBallsT2", MatchStudioHelper.drawFieldPalleLaterali(tmpEventList, fixturePlayersMap));

        tmpEventList = new ArrayList();
        eventTypeId = 12L;
        tagType = null;
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            tmpEventList = groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        killerPassAssist = new ArrayList();
        eventTypeId = 12L;
        tagType = "75";
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            killerPassAssist = groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType);
        }
        sideBalls = new ArrayList();
        eventTypeId = 100L;
        tagType = null;
        if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
            for (Event event : groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType)) {
                if (event.getTagTypeIds() != null && event.getTagTypeIds().contains(2400L)) {
                    continue;
                }
                sideBalls.add(event);
            }
        }
        model.addAttribute("mEventsAnalysisTimeLineT2", MatchStudioHelper.drawPassaggiTimeline(fixtureDetail, tmpEventList, killerPassAssist, sideBalls, locale));
    }

    public static void populateOffensiveIndex(Map<Long, Map<Long, Map<Long, Map<String, List<Event>>>>> groupedEvents, Map<Long, Player> playersMap, FixtureDetails fixtureDetail, ModelMap model, Fixture fixture, Map<Long, FixturePlayer> fixturePlayersMap) {
        // MAPPING OI_EVENT_TYPE
        List<IndexEventType> eventTypeIds = uService.getIndexEventType();
        // OI_TYPE, OI_EVENT_TYPE
        Map<Long, List<Long>> groupedEventTypeIds = new HashMap<>();
        Map<Long, IndexEventType> groupedEventTypes = new HashMap<>();

        if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
            for (IndexEventType eventType : eventTypeIds) {
                if (eventType.getIndexTypeId() != null) {
                    groupedEventTypeIds.putIfAbsent(eventType.getIndexTypeId(), new ArrayList<Long>());
                    groupedEventTypeIds.get(eventType.getIndexTypeId()).add(eventType.getId());

                    groupedEventTypes.putIfAbsent(eventType.getId(), eventType);
                }
            }

            // MAPPING OI_EVENT
            List<IndexEvent> indexEvents = uService.getFixtureIndexEvent(fixture.getId());
            // TEAM, OI_EVENT_TYPE, OI_EVENT
            Map<Long, Map<Long, IndexEvent>> groupedIndexEvents = new HashMap<>();

            if (indexEvents != null && !indexEvents.isEmpty()) {
                for (IndexEvent indexEvent : indexEvents) {
                    if (indexEvent.getTeamId() != null) {
                        groupedIndexEvents.putIfAbsent(indexEvent.getTeamId(), new HashMap<Long, IndexEvent>());
                        groupedIndexEvents.get(indexEvent.getTeamId()).putIfAbsent(indexEvent.getIndexEventTypeId(), indexEvent);
                    }
                }

                List<IndexTrend> indexTrends = uService.getFixtureIndexTrend(fixture.getId());
                // TEAM, OI_TREND
                Map<Long, List<IndexTrend>> groupedIndexTrends = new HashMap<>();

                if (indexTrends != null && !indexTrends.isEmpty()) {
                    for (IndexTrend indexTrend : indexTrends) {
                        if (indexTrend.getTeamId() != null) {
                            groupedIndexTrends.putIfAbsent(indexTrend.getTeamId(), new ArrayList<IndexTrend>());
                            groupedIndexTrends.get(indexTrend.getTeamId()).add(indexTrend);
                        }
                    }

                    // CALCOLO TOTALE INDICI - TEAM 1
                    if (groupedIndexTrends.containsKey(fixture.getHomeTeamId())) {
                        double totalOnOpenPlay = 0, totalOnSetPieces = 0;

                        for (IndexTrend indexTrend : groupedIndexTrends.get(fixture.getHomeTeamId())) {
                            if (indexTrend.getIndexEventTypeId() != null) {
                                if (indexTrend.getDelta() != null) {
                                    if (groupedEventTypeIds.containsKey(0L) && groupedEventTypeIds.get(0L).contains(indexTrend.getIndexEventTypeId())) {
                                        totalOnOpenPlay += indexTrend.getDelta();
                                    } else if (groupedEventTypeIds.containsKey(1L) && groupedEventTypeIds.get(1L).contains(indexTrend.getIndexEventTypeId())) {
                                        totalOnSetPieces += indexTrend.getDelta();
                                    }
                                }
                            }
                        }

                        totalOnOpenPlay = Math.round(Math.round(totalOnOpenPlay * 100.0) / 100.0);
                        totalOnSetPieces = Math.round(Math.round(totalOnSetPieces * 100.0) / 100.0);

                        model.addAttribute("mOITotalOnOpenPlayT1", totalOnOpenPlay);
                        model.addAttribute("mOITotalOnSetPiecesT1", totalOnSetPieces);
                        model.addAttribute("mOITotalT1", totalOnOpenPlay + totalOnSetPieces);
                    }

                    // CALCOLO TOTALE INDICI - TEAM 2
                    if (groupedIndexTrends.containsKey(fixture.getAwayTeamId())) {
                        double totalOnOpenPlay = 0, totalOnSetPieces = 0;

                        for (IndexTrend indexTrend : groupedIndexTrends.get(fixture.getAwayTeamId())) {
                            if (indexTrend.getIndexEventTypeId() != null) {
                                if (indexTrend.getDelta() != null) {
                                    if (groupedEventTypeIds.containsKey(0L) && groupedEventTypeIds.get(0L).contains(indexTrend.getIndexEventTypeId())) {
                                        totalOnOpenPlay += indexTrend.getDelta();
                                    } else if (groupedEventTypeIds.containsKey(1L) && groupedEventTypeIds.get(1L).contains(indexTrend.getIndexEventTypeId())) {
                                        totalOnSetPieces += indexTrend.getDelta();
                                    }
                                }
                            }
                        }

                        totalOnOpenPlay = Math.round(Math.round(totalOnOpenPlay * 100.0) / 100.0);
                        totalOnSetPieces = Math.round(Math.round(totalOnSetPieces * 100.0) / 100.0);

                        model.addAttribute("mOITotalOnOpenPlayT2", totalOnOpenPlay);
                        model.addAttribute("mOITotalOnSetPiecesT2", totalOnSetPieces);
                        model.addAttribute("mOITotalT2", totalOnOpenPlay + totalOnSetPieces);
                    }

                    // DATI GRAFICO - TEAM 1
                    Map<Long, IndexTrend> homeTrendGroupedByMinute = new HashMap<>();
                    Map<Long, IndexTrend> awayTrendGroupedByMinute = new HashMap<>();

                    for (IndexTrend indexTrend : groupedIndexTrends.get(fixture.getHomeTeamId())) {
                        long periodMinute = 0;
                        if (indexTrend.getPeriodMinute() != null && indexTrend.getPeriodMinute() > 0) {
                            if (indexTrend.getPeriodId() < 3) {
                                long timeDuration = 45;

                                periodMinute = (timeDuration * (indexTrend.getPeriodId() - 1)) + indexTrend.getPeriodMinute();
                            } else {
                                long timeDuration = 45;
                                long extraTimeDuration = 15;

                                periodMinute = (timeDuration * (indexTrend.getPeriodId() - 3) + extraTimeDuration * (indexTrend.getPeriodId() - 3)) + indexTrend.getPeriodMinute();
                            }
                        }

                        homeTrendGroupedByMinute.put(periodMinute, indexTrend);
                    }
                    for (IndexTrend indexTrend : groupedIndexTrends.get(fixture.getAwayTeamId())) {
                        long periodMinute = 0;
                        if (indexTrend.getPeriodMinute() != null && indexTrend.getPeriodMinute() > 0) {
                            if (indexTrend.getPeriodId() < 3) {
                                long timeDuration = 45;

                                periodMinute = (timeDuration * (indexTrend.getPeriodId() - 1)) + indexTrend.getPeriodMinute();
                            } else {
                                long timeDuration = 45;
                                long extraTimeDuration = 15;

                                periodMinute = (timeDuration * (indexTrend.getPeriodId() - 3) + extraTimeDuration * (indexTrend.getPeriodId() - 3)) + indexTrend.getPeriodMinute();
                            }
                        }

                        awayTrendGroupedByMinute.put(periodMinute, indexTrend);
                    }

                    // DATI GRAFICO TREND
                    // MINUTO, TEAM
                    long eventTypeId = 25L;
                    String tagType = null;
                    StringBuilder goalDatas = new StringBuilder("[");
                    if (groupedEvents.get(fixture.getHomeTeamId()).get(null).containsKey(eventTypeId)
                        && groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        for (Event event : groupedEvents.get(fixture.getHomeTeamId()).get(null).get(eventTypeId).get(tagType)) {
                            long periodMinute = Math.round((double) (event.getStartMinute() + ((event.getPeriod() - 1) * 45)));
                            goalDatas.append("{" + "\"minute\": ").append(periodMinute).append(",").append("\"team\": ").append(event.getTeamId()).append(",").append("\"ballIcon\": ").append("\"/sicsdataanalytics/images/matchstudio/football-ball-aut.svg\"").append("},");
                        }
                    }
                    if (groupedEvents.get(fixture.getAwayTeamId()).get(null).containsKey(eventTypeId)
                            && groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).containsKey(tagType)) {
                        for (Event event : groupedEvents.get(fixture.getAwayTeamId()).get(null).get(eventTypeId).get(tagType)) {
                            long periodMinute = Math.round((double) (event.getStartMinute() + ((event.getPeriod() - 1) * 45)));
                            goalDatas.append("{" + "\"minute\": ").append(periodMinute).append(",").append("\"team\": ").append(event.getTeamId()).append(",").append("\"ballIcon\": ").append("\"/sicsdataanalytics/images/matchstudio/football-ball.svg\"").append("},");
                        }
                    }
                    // remove last comma
                    if (goalDatas.length() > 1) {
                        goalDatas.deleteCharAt(goalDatas.length() - 1);
                    }
                    goalDatas.append("]");

                    double homeOi = 0, awayOi = 0;
                    StringBuilder chartDatas = new StringBuilder("[");
                    for (long i = 0; i < fixtureDetail.getMatchMinutes(); i++) {
                        long periodMinute = i;
                        if (homeTrendGroupedByMinute.containsKey(periodMinute)) {
                            homeOi = homeTrendGroupedByMinute.get(periodMinute).getOi();
                        }
                        if (awayTrendGroupedByMinute.containsKey(periodMinute)) {
                            awayOi = awayTrendGroupedByMinute.get(periodMinute).getOi();
                        }
                        chartDatas.append("{" + "\"minute\": ").append(periodMinute).append(",").append("\"home\": ").append(homeOi).append(",").append("\"away\": ").append(awayOi).append("},");
                    }
                    // remove last comma
                    if (chartDatas.length() > 1) {
                        chartDatas.deleteCharAt(chartDatas.length() - 1);
                    }
                    chartDatas.append("]");

                    // CALCOLO MINUTAGGIO FINE PRIMO TEMPO
                    int timeBreak = 45;
                    if (fixtureDetail.getEndTime1() != null && fixtureDetail.getEndTime1() > 0) {
                        timeBreak = Math.toIntExact((fixtureDetail.getEndTime1() - fixtureDetail.getStartTime1()) / 1000 / 60);
                    }

                    model.addAttribute("mOITrendChartData", chartDatas.toString());
                    model.addAttribute("mOITrendChartGoalsData", goalDatas.toString());
                    model.addAttribute("mOITrendChartBreak", timeBreak);
                    model.addAttribute("mOIGroupedEventTypes", groupedEventTypes);
                    model.addAttribute("mOIGroupedEvents", groupedIndexEvents);
                }
            }
        }
    }

    private static void increaseTableValue(StudioTableValue value, Event event, FixtureDetails fixtureDetail, FixturePlayer fixturePlayer) {
        increaseTableValue(value, event, fixtureDetail, fixturePlayer, null);
    }

    private static void increaseTableValue(StudioTableValue value, Event event, FixtureDetails fixtureDetail, FixturePlayer fixturePlayer, ValueType valueToSum) {
        if (isPlaying(event, fixtureDetail, fixturePlayer)) {
            value.increaseTotal();
            if (event.getPeriod() != null) {
                if (Integer.compare(event.getPeriod(), 1) == 0) {
                    value.increaseFirstHalf();
                } else if (Integer.compare(event.getPeriod(), 2) == 0) {
                    value.increaseSecondHalf();
                }
            }

            if (valueToSum != null) {
                if (valueToSum == ValueType.DISTANCE) {
                    if (event.getDistance() != null) {
                        value.increaseTmpValue(event.getDistance());
                        if (event.getPeriod() != null) {
                            if (Integer.compare(event.getPeriod(), 1) == 0) {
                                value.increaseTmpValueFirstHalf(event.getDistance());
                            } else if (Integer.compare(event.getPeriod(), 2) == 0) {
                                value.increaseTmpValueSecondHalf(event.getDistance());
                            }
                        }
                    }
                }
            }
        }
    }

    private static List<Event> getPlayerTouches(Map<Long, Map<String, List<Event>>> events) {
        List<Event> touches = new ArrayList<>();

        if (events != null) {
            if (events.containsKey(34L) && events.get(34L).containsKey(null)) {
                touches.addAll(events.get(34L).get(null));  // Passaggi
            }
            if (events.containsKey(9L) && events.get(9L).containsKey(null)) {
                touches.addAll(events.get(9L).get(null));   // Duelli
            }
            if (events.containsKey(23L) && events.get(23L).containsKey(null)) {
                touches.addAll(events.get(23L).get(null));  // Duelli
            }
            if (events.containsKey(5L) && events.get(5L).containsKey(null)) {
                touches.addAll(events.get(5L).get(null));   // Tiri
            }
            if (events.containsKey(737L) && events.get(737L).containsKey(null)) {
                touches.addAll(events.get(737L).get(null)); // Controllo Palla Errato
            }
            if (events.containsKey(16L) && events.get(16L).containsKey(null)) {
                touches.addAll(events.get(16L).get(null));  // Parate
            }
            if (events.containsKey(15L) && events.get(15L).containsKey(null)) {
                touches.addAll(events.get(15L).get(null));  // Uscite
                if (events.containsKey(704L) && events.get(704L).containsKey(null)) {
                    for (Event intercept : events.get(704L).get(null)) {
                        boolean coincide = false;
                        for (Event uscita : events.get(15L).get(null)) {
                            int puntoMedio = (int) ((uscita.getEndMillis() + uscita.getStartMillis()) / 2);
                            if (intercept.getStartMillis() > puntoMedio && puntoMedio < intercept.getEndMillis()) {
                                coincide = true;
                                break;
                            }
                        }
                        if (!coincide) {
                            touches.add(intercept);
                        }
                    }
                }
            } else {
                if (events.containsKey(704L) && events.get(704L).containsKey(null)) {
                    touches.addAll(events.get(704L).get(null));  // Intercetta
                }
            }
        }

        return touches;
    }

    private static boolean isPlaying(Event event, FixtureDetails fixtureDetail, FixturePlayer fixturePlayer) {
        boolean playing = true;
        Long eventTime = event.getStartMillis();

        // 1. coordinate
        if (StringUtils.isNotBlank(fixturePlayer.getCoordinate())) {
            if (StringUtils.equals(fixturePlayer.getCoordinate(), "-1,0;-1,0")) {
                playing = false;
            }
        } else {
            playing = false;
        }

        // 2. espulso
        if (fixturePlayer.getRedCardTime() != null) {
            if (fixturePlayer.getRedCardTime() != -1L && fixturePlayer.getRedCardTime() < eventTime) {
                playing = false;
            }
        } else {
            playing = false;
        }

        // 3. sostituzione
        if (eventTime != null && fixturePlayer.getSubstituteTime() != null) {
            if (fixturePlayer.getSubstituteTime() != -1L && fixturePlayer.getSubstituteTime() < eventTime) {
                playing = false;
            }
        } else {
            playing = false;
        }

        // 4. subentro
        if (eventTime != null && fixtureDetail != null && fixturePlayer.getFromPeriodId() != null && fixturePlayer.getFromPeriodMinute() != null) {
            if (event.getPeriod() != null) {
                switch (event.getPeriod()) {
                    case 1:
                        if (fixtureDetail.getStartTime1() != null) {
                            if (fixtureDetail.getStartTime1() + (fixturePlayer.getFromPeriodMinute() * 60000) > eventTime) {
                                playing = false;
                            }
                        } else {
                            playing = false;
                        }
                        break;
                    case 2:
                        if (fixtureDetail.getStartTime2() != null) {
                            if (fixtureDetail.getStartTime2() + (fixturePlayer.getFromPeriodMinute() * 60000) > eventTime) {
                                playing = false;
                            }
                        } else {
                            playing = false;
                        }
                        break;
                    case 3:
                        if (fixtureDetail.getStartTime3() != null) {
                            if (fixtureDetail.getStartTime3() + (fixturePlayer.getFromPeriodMinute() * 60000) > eventTime) {
                                playing = false;
                            }
                        } else {
                            playing = false;
                        }
                        break;
                    case 4:
                        if (fixtureDetail.getStartTime4() != null) {
                            if (fixtureDetail.getStartTime4() + (fixturePlayer.getFromPeriodMinute() * 60000) > eventTime) {
                                playing = false;
                            }
                        } else {
                            playing = false;
                        }
                        break;
                    default:
                        playing = false;
                        break;
                }
            } else {
                playing = false;
            }
        } else {
            playing = false;
        }
        return playing;
    }

    private static boolean isActionInside(Event event, List<Event> eventList) {
        boolean inside = false;
        for (Event tmpEvent : eventList) {
            if ((event.getStartMillis() >= tmpEvent.getStartMillis() && event.getStartMillis() <= tmpEvent.getEndMillis())
                    || (event.getStartMillis() + (event.getEndMillis() - event.getStartMillis()) / 2 >= tmpEvent.getStartMillis() && event.getStartMillis() + (event.getEndMillis() - event.getStartMillis()) / 2 <= tmpEvent.getEndMillis())) {
                inside = true;
                break;
            }
        }
        return inside;

    }

    private enum ValueType {
        DISTANCE;
    }
}
