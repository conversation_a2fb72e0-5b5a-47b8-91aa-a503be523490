package sics.controller;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import static sics.controller.BaseController.checkCache;
import sics.domain.Filter;
import sics.domain.Season;
import sics.domain.User;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;

@Controller
@RequestMapping("/team")
public class TeamController extends BaseController {

    //inserire le location del mapping qui sotto come stringhe
    private final static String PAGE_HOME = "team/home.jsp";

    private final static String PAGE_RANKING = "team/ranking.jsp";
    private final static String PAGE_TREND = "team/trend.jsp";
    private final static String PAGE_OVERVIEW = "team/overview.jsp";
    private final static String PAGE_POSITIONAL = "team/positional.jsp";
    private final static String PAGE_SCATTERPLOT = "team/scatterplot.jsp";
    private final static String PAGE_RADAR = "team/radar.jsp";
    private final static String PAGE_MATCHES = "team/matches.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/home")
    public String home(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");
        model.addAttribute("mIsHomePage", true);

        return PAGE_HOME;
    }

    @RequestMapping("/ranking")
    public String ranking(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-ranking");
        model.addAttribute("mFilters", filters);

        return PAGE_RANKING;
    }

    @RequestMapping("/trend")
    public String trend(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-trend");
        model.addAttribute("mFilters", filters);

        return PAGE_TREND;
    }

    @RequestMapping("/overview")
    public String overview(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-overview");
        model.addAttribute("mFilters", filters);

        return PAGE_OVERVIEW;
    }

    @RequestMapping("/positional")
    public String positional(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-positional");
        model.addAttribute("mFilters", filters);

        return PAGE_POSITIONAL;
    }

    @RequestMapping("/scatterplot")
    public String scatterplot(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-scatterplot");
        model.addAttribute("mFilters", filters);

        return PAGE_SCATTERPLOT;
    }

    @RequestMapping("/radar")
    public String radar(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-radar");
        model.addAttribute("mFilters", filters);

        return PAGE_RADAR;
    }

    @RequestMapping("/matches")
    public String matches(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "team");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "team-matches");
        model.addAttribute("mFilters", filters);

        return PAGE_MATCHES;
    }

    @RequestMapping("/errorTest")
    public void errorTest(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        checkCache(true);
        // throw new NullPointerException("test errore");
    }
}
