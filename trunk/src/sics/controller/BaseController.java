package sics.controller;

import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.ui.ModelMap;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.AdvancedMetric;
import sics.domain.Competition;
import sics.domain.Country;
import sics.domain.EventType;
import sics.domain.FixtureDetails;
import sics.domain.Foot;
import sics.domain.Player;
import sics.domain.Position;
import sics.domain.Season;
import sics.domain.Settings;
import sics.domain.TagType;
import sics.domain.Team;
import sics.domain.TeamPlayer;
import sics.domain.User;
import sics.helper.GlobalHelper;
import sics.listener.SessionListener;
import sics.service.UserService;
import sics.websocket.MessageType;
import sics.websocket.WebSocketEndpoint;
import javax.websocket.Session;
import sics.domain.Group;
import sics.domain.PlayerAgency;
import sics.helper.SpringApplicationContextHelper;

public class BaseController {

    protected transient final Log log = LogFactory.getLog(getClass());
    protected final static String PAGE_REDIRECT = "redirect:";
    protected final static String PAGE_403 = "auth/403.jsp";

    protected static Map<Long, Season> seasons = new HashMap<>();
    protected static Map<Long, Season> groupedSeasons = new HashMap<>();
    protected static Map<Long, Team> teams = new HashMap<>();
    protected static Map<Long, Competition> competitions = new HashMap<>();
    protected static Map<Long, Competition> internationalCompetitions = new HashMap<>();
    protected static Map<Long, EventType> eventTypes = new HashMap<>();
    protected static Map<Long, EventType> oppositeEventTypes = new HashMap<>();
    // protected static List<Long> sufferedEventTypes = new ArrayList<>(Arrays.asList(2L, 4L, 6L, 11L, 13L, 21L, 22L, 24L, 26L, 27L, 101L, 103L, 1008L, 1011L, 1012L, 1013L)); // 1000+ sono id di stats_type (metriche avanzate)
    protected static List<Long> sufferedEventTypes = new ArrayList<>(Arrays.asList(31L, 27L, 32L, 22L, 23L, 9L, 19L, 18L, 704L, 101L, 4L, 13L, 11L, 26L, 6L, 103L, 16L, 15L, 1012L, 1008L, 1001L, 1011L, 2L, 1277L, 1275L, 1279L)); // 1000+ sono id di stats_type (metriche avanzate)
    protected static Map<Long, TagType> tagTypes = new HashMap<>();
    protected static Map<Long, TagType> oppositeTagTypes = new HashMap<>();
    protected static Map<Long, Player> players = new HashMap<>();
    protected static Map<Long, FixtureDetails> fixtureDetails = new HashMap<>();
    protected static Map<Long, Country> countries = new HashMap<>();
    protected static Map<Long, List<Country>> internationalCountriesMap = new HashMap<>();
    protected static Map<Integer, Foot> foots = new HashMap<>();
    protected static Map<Integer, Position> positions = new HashMap<>();
    protected static Map<Integer, Position> positionDetails = new HashMap<>();
    protected static Map<Integer, List<Position>> positionDetailMap = new HashMap<>();
    protected static Map<Long, Map<Long, Map<Long, TeamPlayer>>> seasonTeamPlayerPositions = new HashMap<>();
    protected static Map<Long, AdvancedMetric> advancedMetrics = new HashMap<>();
    protected static Map<Long, EventType> tacticalEventTypes = new HashMap<>();
    protected static Map<Long, List<TagType>> eventTypeTagList = new HashMap<>();
    protected static Map<Long, AdvancedMetric> similarityMetrics = new HashMap<>();
    protected static Map<Long, EventType> playerEventTypes = new HashMap<>();
    protected static Map<Long, AdvancedMetric> playerAdvancedMetrics = new HashMap<>();
    protected static Map<Long, EventType> playerTacticalEventTypes = new HashMap<>();
    protected static Map<Long, PlayerAgency> playerAgencies = new HashMap<>();
    protected static Map<Integer, Group> groups = new HashMap<>();
    protected static Timer timer = new Timer("Cache Reloader");

    static {
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                try {
                    checkCache(true);
                } catch (Exception ex) {
                    GlobalHelper.reportError(ex);
                }
            }
        };

        long period = 900000L;
        timer.scheduleAtFixedRate(task, 0, period); // ogni 15 minuti aggiorno la cache
    }

    public static void killTimer() {
        if (timer != null) {
            timer.cancel();
            timer.purge();
            timer = null;
        }
    }

    private static UserService mService = new UserService();

    /*

        START CACHE

     */
    public static void checkCache() {
        checkCache(false);
    }

    public static void checkCache(Boolean force) {
        Date now = new Date();

        if (teams.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Team> teamList = mService.getTeams();
            if (teamList != null && !teamList.isEmpty()) {
                Map<Long, Team> tmpTeams = new HashMap<>();
                for (Team team : teamList) {
                    tmpTeams.put(team.getId(), team);
                }

                Team competitionAverage = new Team();
                competitionAverage.setId(0L);
                competitionAverage.setName("MEDIA COMPETIZIONE");
                competitionAverage.setNameEn("COMPETITION AVERAGE");
                competitionAverage.setLogo("unknownxx");
                tmpTeams.put(0L, competitionAverage);

                teams = tmpTeams;
            }
        }
//        System.out.println("(!) teams loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (seasons.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Season> seasonList = mService.getSeasons();
            if (seasonList != null && !seasonList.isEmpty()) {
                Map<Long, Season> tmpSeasons = new HashMap<>();
                for (Season season : seasonList) {
                    tmpSeasons.put(season.getId(), season);
                }
                seasons = tmpSeasons;

                // creo qua le stagioni che saranno visibili in pagina
                groupedSeasons = new HashMap<>();
                List<Season> nonSolarSeasons = new ArrayList<>();
                List<Season> solarSeasons = new ArrayList<>();
                for (Season season : seasons.values()) {
                    if (BooleanUtils.isTrue(season.getVisible())) {
                        if (season.getId() >= 2000) {
                            nonSolarSeasons.add(season);
                        } else {
                            solarSeasons.add(season);
                        }
                    }
                }

                Collections.sort(nonSolarSeasons, new Comparator<Season>() {
                    @Override
                    public int compare(Season o1, Season o2) {
                        return o2.getId().compareTo(o1.getId());
                    }
                });
                Collections.sort(solarSeasons, new Comparator<Season>() {
                    @Override
                    public int compare(Season o1, Season o2) {
                        return o2.getId().compareTo(o1.getId());
                    }
                });

                int maxSize = Math.max(nonSolarSeasons.size(), solarSeasons.size());
                for (int i = 0; i < maxSize; i++) {
                    Season nonSolarSeason = null, solarSeason = null;
                    if (nonSolarSeasons.size() > i) {
                        nonSolarSeason = nonSolarSeasons.get(i);
                    }
                    if (solarSeasons.size() > i) {
                        solarSeason = solarSeasons.get(i);
                    }

                    Season groupedSeason = new Season();
                    if (nonSolarSeason != null && solarSeason != null) {
                        groupedSeason.setId(nonSolarSeason.getId());
                        // 24/25 - 2025. Uso ID per cercare di essere più sicuro
                        groupedSeason.setName((nonSolarSeason.getId() - 2000) + "/" + (nonSolarSeason.getId() - 1999) + " - " + (solarSeason.getId() + 2000));
                        groupedSeason.setVisible(true);
                        groupedSeason.setNonSolarId(nonSolarSeason.getId());
                        groupedSeason.setSolarId(solarSeason.getId());
                        groupedSeasons.put(nonSolarSeason.getId(), groupedSeason);
                    } else if (nonSolarSeason != null) {
                        // ho solo stagione non solare
                        groupedSeason.setId(nonSolarSeason.getId());
                        // 24/25 - 2025. Uso ID per cercare di essere più sicuro
                        groupedSeason.setName((nonSolarSeason.getId() - 2000) + "/" + (nonSolarSeason.getId() - 1999));
                        groupedSeason.setVisible(true);
                        groupedSeason.setNonSolarId(nonSolarSeason.getId());
                        groupedSeasons.put(nonSolarSeason.getId(), groupedSeason);
                    } else if (solarSeason != null) {
                        // ho solo stagione solare
                        groupedSeason.setId(solarSeason.getId());
                        // 24/25 - 2025. Uso ID per cercare di essere più sicuro
                        groupedSeason.setName((solarSeason.getId() + 2000) + "");
                        groupedSeason.setVisible(true);
                        groupedSeason.setSolarId(solarSeason.getId());
                        groupedSeasons.put(solarSeason.getId(), groupedSeason);
                    }
                }
//                for (Season season : nonSolarSeasons) {
//                    int index = nonSolarSeasons.indexOf(season);
//                    if (solarSeasons.size() > index) {
//                        Season solarSeason = solarSeasons.get(index);
//
//                        Season groupedSeason = new Season();
//                        groupedSeason.setId(season.getId());
//                        // 24/25 - 2025. Uso ID per cercare di essere più sicuro
//                        groupedSeason.setName((season.getId() - 2001) + "/" + (season.getId() - 2000) + " - " + (solarSeason.getId() + 2000));
//                        groupedSeason.setVisible(true);
//                        groupedSeason.setNonSolarId(season.getId());
//                        groupedSeason.setSolarId(solarSeason.getId());
//                        groupedSeasons.put(season.getId(), groupedSeason);
//                    }
//                }
            }
        }
//        System.out.println("(!) seasons loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (competitions.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Competition> competitionList = mService.getCompetitions();
            if (competitionList != null && !competitionList.isEmpty()) {
                Map<Long, Competition> tmpCompetitions = new HashMap<>();
                for (Competition competition : competitionList) {
                    tmpCompetitions.put(competition.getId(), competition);
                }
                competitions = tmpCompetitions;
            }
        }

        if (internationalCompetitions.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Competition> internationalCompetitionList = mService.getInternationalCompetitions();
            if (internationalCompetitionList != null && !internationalCompetitionList.isEmpty()) {
                Map<Long, Competition> tmpInternationalCompetitions = new HashMap<>();
                for (Competition competition : internationalCompetitionList) {
                    tmpInternationalCompetitions.put(competition.getId(), competition);
                }
                internationalCompetitions = tmpInternationalCompetitions;
            }
        }
//        System.out.println("(!) competitions loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (eventTypes.isEmpty() || BooleanUtils.isTrue(force)) {
            List<EventType> eventTypeList = mService.getEventTypes();
            if (eventTypeList != null && !eventTypeList.isEmpty()) {
                Map<Long, EventType> tmpEventTypes = new HashMap<>();
                for (EventType eventType : eventTypeList) {
                    tmpEventTypes.put(eventType.getId(), eventType);
                }
                eventTypes = tmpEventTypes;

                // metriche tattiche
                tacticalEventTypes.put(1274L, eventTypes.get(1274L));
                tacticalEventTypes.put(1275L, eventTypes.get(1275L));
                tacticalEventTypes.put(1276L, eventTypes.get(1276L));
                tacticalEventTypes.put(1277L, eventTypes.get(1277L));
                tacticalEventTypes.put(1278L, eventTypes.get(1278L));
                tacticalEventTypes.put(1279L, eventTypes.get(1279L));
                // Michele 18/11/2024: ha deciso che ATT e DIF vanno dentro alle tattiche
                tacticalEventTypes.put(1L, eventTypes.get(1L));
                tacticalEventTypes.put(2L, eventTypes.get(2L));

                // metriche giocatori (clono perchè altrimenti rimuovo da entrambe le mappe)
                playerEventTypes = new HashMap<>(eventTypes);
                playerEventTypes.remove(927L); // fase di possesso
                playerEventTypes.remove(18L); // fuorigioco provocato
                playerEventTypes.remove(101L); // palle laterali subite
                playerEventTypes.remove(4L); // palle inattive contro
                playerEventTypes.remove(13L); // passaggio chiave subito
                playerEventTypes.remove(103L); // triangolazione subita
                playerEventTypes.remove(3L); // palle inattive favore
                playerEventTypes.remove(6L); // tiro subito
                playerEventTypes.remove(27L); // assist subito
                // matriche tattiche giocatori
                playerEventTypes.remove(1276L); // attacco area
                playerEventTypes.remove(1L); // azione attacco
                playerEventTypes.remove(1274L); // costruzione
                playerEventTypes.remove(1278L); // transizione offensiva
                playerEventTypes.remove(2L); // azione difesa
                playerEventTypes.remove(1277L); // difesa area
                playerEventTypes.remove(1275L); // prima pressione
                playerEventTypes.remove(1279L); // transizione difesiva
            }
        }
//        System.out.println("(!) eventTypes loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (oppositeEventTypes.isEmpty() || BooleanUtils.isTrue(force)) {
            List<EventType> eventTypeList = mService.getOppositeEventTypes();
            if (eventTypeList != null && !eventTypeList.isEmpty()) {
                Map<Long, EventType> tmpOppositeEventTypes = new HashMap<>();
                for (EventType eventType : eventTypeList) {
                    tmpOppositeEventTypes.put(eventType.getId(), eventType);
                }
                oppositeEventTypes = tmpOppositeEventTypes;
            }
        }
//        System.out.println("(!) oppositeEventTypes loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (tagTypes.isEmpty() || BooleanUtils.isTrue(force)) {
            List<TagType> tagTypeList = mService.getTagTypes();
            if (tagTypeList != null && !tagTypeList.isEmpty()) {
                Map<Long, TagType> tmpTagTypes = new HashMap<>();
                for (TagType tagType : tagTypeList) {
                    tmpTagTypes.put(tagType.getId(), tagType);
                }
                tagTypes = tmpTagTypes;
            }

            // mappo evento -> tags
            if (!eventTypes.isEmpty() && !tagTypes.isEmpty()) {
                eventTypeTagList.clear();
                for (Long eventTypeId : eventTypes.keySet()) {
                    for (TagType tagType : tagTypes.values()) {
                        if (StringUtils.isNotBlank(eventTypes.get(eventTypeId).getCode()) && StringUtils.isNotBlank(tagType.getCode())) {
                            if (tagType.getCode().startsWith(eventTypes.get(eventTypeId).getCode() + "-")) {
                                eventTypeTagList.putIfAbsent(eventTypeId, new ArrayList<TagType>());
                                eventTypeTagList.get(eventTypeId).add(tagType);
                            }
                        }
                    }
                }
            }
        }
//        System.out.println("(!) tagTypes loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        // ora posso popolare i tag opposti
        if (oppositeTagTypes.isEmpty() || BooleanUtils.isTrue(force)) {
            if (tagTypes != null && !tagTypes.isEmpty()) {
                oppositeTagTypes.clear();
                for (TagType tagType : tagTypes.values()) {
                    if (BooleanUtils.isTrue(tagType.getIsOpposite())) {
                        oppositeTagTypes.put(tagType.getId(), tagType);
                    }
                }
            }
        }
//        System.out.println("(!) oppositeTagTypes loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (players.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Player> playerList = mService.getPlayers();
            if (playerList != null && !playerList.isEmpty()) {
                Map<Long, Player> tmpPlayers = new HashMap<>();
                for (Player player : playerList) {
                    tmpPlayers.put(player.getId(), player);
                }

                Player roleAverage = new Player();
                roleAverage.setId(0L);
                roleAverage.setFirstName("ROLE");
                roleAverage.setLastName("AVERAGE");
                roleAverage.setKnownName("ROLE AVERAGE");
                roleAverage.setPhoto("unknownxx");
                tmpPlayers.put(0L, roleAverage);

                players = tmpPlayers;
            }
        }
//        System.out.println("(!) players loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (fixtureDetails.isEmpty() || BooleanUtils.isTrue(force)) {
            List<FixtureDetails> fixturedetailsList = mService.getFixtureDetails();
            if (fixturedetailsList != null && !fixturedetailsList.isEmpty()) {
                Map<Long, FixtureDetails> tmpFixtureDetails = new HashMap<>();
                for (FixtureDetails fixtureDetail : fixturedetailsList) {
                    tmpFixtureDetails.put(fixtureDetail.getFixtureId(), fixtureDetail);
                }
                fixtureDetails = tmpFixtureDetails;
            }
        }
//        System.out.println("(!) fixtureDetails loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (countries.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Country> countriesList = mService.getCountries();
            if (countriesList != null && !countriesList.isEmpty()) {
                Map<Long, Country> tmpCountries = new HashMap<>();
                for (Country country : countriesList) {
                    tmpCountries.put(country.getId(), country);
                }
                countries = tmpCountries;
            }
        }

        if (internationalCountriesMap.isEmpty() || BooleanUtils.isTrue(force)) {
            if (!countries.isEmpty()) {
                internationalCountriesMap.clear();
                for (Country country : countries.values()) {
                    if (country.getInternationalCompetitionId() != null) {
                        internationalCountriesMap.putIfAbsent(country.getInternationalCompetitionId(), new ArrayList<Country>());
                        internationalCountriesMap.get(country.getInternationalCompetitionId()).add(country);
                    }
                }
            }
        }
//        System.out.println("(!) countries loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (foots.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Foot> footList = mService.getFoots();
            if (footList != null && !footList.isEmpty()) {
                Map<Integer, Foot> tmpFoots = new HashMap<>();
                for (Foot foot : footList) {
                    tmpFoots.put(foot.getId().intValue(), foot);
                }
                foots = tmpFoots;
            }
        }
//        System.out.println("(!) foots loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (positions.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Position> positionList = mService.getPositions();
            if (positionList != null && !positionList.isEmpty()) {
                Map<Integer, Position> tmpPositions = new HashMap<>();
                for (Position position : positionList) {
                    tmpPositions.put(position.getId().intValue(), position);
                }
                positions = tmpPositions;
            }
        }
//        System.out.println("(!) positions loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (positionDetails.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Position> positionDetailList = mService.getPositionDetails();
            if (positionDetailList != null && !positionDetailList.isEmpty()) {
                Map<Integer, Position> tmpPositionDetails = new HashMap<>();
                for (Position positionDetail : positionDetailList) {
                    tmpPositionDetails.put(positionDetail.getId().intValue(), positionDetail);
                }
                positionDetails = tmpPositionDetails;
            }
        }

        if (positionDetailMap.isEmpty() || BooleanUtils.isTrue(force)) {
            if (!positionDetails.isEmpty()) {
                positionDetailMap.clear();
                for (Position positionDetail : positionDetails.values()) {
                    positionDetailMap.putIfAbsent(positionDetail.getPositionId(), new ArrayList<Position>());
                    positionDetailMap.get(positionDetail.getPositionId()).add(positionDetail);
                }
            }
        }
//        System.out.println("(!) positionDetails loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (seasonTeamPlayerPositions.isEmpty() || BooleanUtils.isTrue(force)) {
            List<TeamPlayer> teamPlayerList = mService.getTeamPlayers();
            if (teamPlayerList != null && !teamPlayerList.isEmpty()) {
                Map<Long, Map<Long, Map<Long, TeamPlayer>>> tmpSeasonTeamPlayerPositions = new HashMap<>();
                for (TeamPlayer teamPlayer : teamPlayerList) {
                    if (teamPlayer.getSeasonId() != null && teamPlayer.getTeamId() != null && teamPlayer.getPlayerId() != null) {
                        tmpSeasonTeamPlayerPositions.putIfAbsent(teamPlayer.getSeasonId(), new HashMap<Long, Map<Long, TeamPlayer>>());
                        tmpSeasonTeamPlayerPositions.get(teamPlayer.getSeasonId()).putIfAbsent(teamPlayer.getTeamId(), new HashMap<Long, TeamPlayer>());
                        tmpSeasonTeamPlayerPositions.get(teamPlayer.getSeasonId()).get(teamPlayer.getTeamId()).put(teamPlayer.getPlayerId(), teamPlayer);
                    }
                }
                seasonTeamPlayerPositions = tmpSeasonTeamPlayerPositions;
            }
        }
//        System.out.println("(!) seasonTeamPlayerPositions loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (advancedMetrics.isEmpty() || BooleanUtils.isTrue(force)) {
            List<AdvancedMetric> advancedMetricList = mService.getAdvancedMetrics();
            if (advancedMetricList != null && !advancedMetricList.isEmpty()) {
                Map<Long, AdvancedMetric> tmpAdvancedMetrics = new HashMap<>();
                for (AdvancedMetric advancedMetric : advancedMetricList) {
                    tmpAdvancedMetrics.put(advancedMetric.getId(), advancedMetric);

                    // gestione metriche avanzate opposte
                    if (BooleanUtils.isTrue(advancedMetric.getIsOpposite())) {
                        if (!oppositeEventTypes.containsKey(advancedMetric.getId())) {
                            EventType tmpEventType = new EventType();
                            tmpEventType.setId(advancedMetric.getId());
                            tmpEventType.setCode(advancedMetric.getCode());
                            tmpEventType.setDesc(advancedMetric.getDesc());
                            tmpEventType.setDescEn(advancedMetric.getDescEn());
                            tmpEventType.setDescFr(advancedMetric.getDescFr());
                            tmpEventType.setDescEs(advancedMetric.getDescEs());
                            oppositeEventTypes.put(advancedMetric.getId(), tmpEventType);
                        }
                    }
                }

                // Metrica Punti
                AdvancedMetric points = new AdvancedMetric();
                points.setCode("homePoints");
                points.setId(999L);
                points.setDesc(SpringApplicationContextHelper.getMessage("event.points", Locale.ITALIAN));
                points.setDescEn(SpringApplicationContextHelper.getMessage("event.points", Locale.ENGLISH));
                points.setDescFr(SpringApplicationContextHelper.getMessage("event.points", Locale.FRENCH));
                points.setDescEs(SpringApplicationContextHelper.getMessage("event.points", new Locale("es", "ES")));
                tmpAdvancedMetrics.put(999L, points);
                advancedMetrics = tmpAdvancedMetrics;

                // metriche avanzate giocatori (clono perchè altrimenti rimuovo da entrambe le mappe)
                playerAdvancedMetrics = new HashMap<>(advancedMetrics);
                playerAdvancedMetrics.remove(1010L); // field tilt
                playerAdvancedMetrics.remove(1000L); // offensive index
                playerAdvancedMetrics.remove(1001L); // defensive index
                playerAdvancedMetrics.remove(1017L); // offensive index - defensive index
                playerAdvancedMetrics.remove(999L); // points
                playerAdvancedMetrics.remove(217L); // possesso palla
                playerAdvancedMetrics.remove(1002L); // possesso palla (statistica)
                playerAdvancedMetrics.remove(1011L); // ppda
                playerAdvancedMetrics.remove(1008L); // conceded xg
            }
        }

        if (similarityMetrics.isEmpty() || BooleanUtils.isTrue(force)) {
            List<AdvancedMetric> similarityMetricsList = mService.getSimilarityMetrics();
            if (similarityMetricsList != null && !similarityMetricsList.isEmpty()) {
                Map<Long, AdvancedMetric> tmpSimilarityMetrics = new HashMap<>();
                for (AdvancedMetric similarityMetric : similarityMetricsList) {
                    tmpSimilarityMetrics.put(similarityMetric.getId(), similarityMetric);
                }

                // Metrica % Precisione Passaggi
                AdvancedMetric percPassPrecision = new AdvancedMetric();
                percPassPrecision.setCode("passPrecisionPerc");
                percPassPrecision.setId(900L);
                percPassPrecision.setDesc(SpringApplicationContextHelper.getMessage("metric.perc.passes", Locale.ITALIAN));
                percPassPrecision.setDescEn(SpringApplicationContextHelper.getMessage("metric.perc.passes", Locale.ENGLISH));
                percPassPrecision.setDescFr(SpringApplicationContextHelper.getMessage("metric.perc.passes", Locale.FRENCH));
                percPassPrecision.setDescEs(SpringApplicationContextHelper.getMessage("metric.perc.passes", new Locale("es", "ES")));
                percPassPrecision.setIsPercentage(true);
                tmpSimilarityMetrics.put(900L, percPassPrecision);

                // Metrica % Duelli Vinti
                AdvancedMetric percDuelWon = new AdvancedMetric();
                percDuelWon.setCode("duelWonPerc");
                percDuelWon.setId(901L);
                percDuelWon.setDesc(SpringApplicationContextHelper.getMessage("metric.perc.duel.won", Locale.ITALIAN));
                percDuelWon.setDescEn(SpringApplicationContextHelper.getMessage("metric.perc.duel.won", Locale.ENGLISH));
                percDuelWon.setDescFr(SpringApplicationContextHelper.getMessage("metric.perc.duel.won", Locale.FRENCH));
                percDuelWon.setDescEs(SpringApplicationContextHelper.getMessage("metric.perc.duel.won", new Locale("es", "ES")));
                percDuelWon.setIsPercentage(true);
                tmpSimilarityMetrics.put(901L, percDuelWon);

                // Metrica % Tiri in Porta
                AdvancedMetric percFieldShots = new AdvancedMetric();
                percFieldShots.setCode("fieldShotsPerc");
                percFieldShots.setId(902L);
                percFieldShots.setDesc(SpringApplicationContextHelper.getMessage("metric.perc.field.shots", Locale.ITALIAN));
                percFieldShots.setDescEn(SpringApplicationContextHelper.getMessage("metric.perc.field.shots", Locale.ENGLISH));
                percFieldShots.setDescFr(SpringApplicationContextHelper.getMessage("metric.perc.field.shots", Locale.FRENCH));
                percFieldShots.setDescEs(SpringApplicationContextHelper.getMessage("metric.perc.field.shots", new Locale("es", "ES")));
                percFieldShots.setIsPercentage(true);
                tmpSimilarityMetrics.put(902L, percFieldShots);

                // Metrica % Parate su tiri in porta subiti
                AdvancedMetric percSaved = new AdvancedMetric();
                percSaved.setCode("savedPerc");
                percSaved.setId(903L);
                percSaved.setDesc(SpringApplicationContextHelper.getMessage("metric.perc.saved", Locale.ITALIAN));
                percSaved.setDescEn(SpringApplicationContextHelper.getMessage("metric.perc.saved", Locale.ENGLISH));
                percSaved.setDescFr(SpringApplicationContextHelper.getMessage("metric.perc.saved", Locale.FRENCH));
                percSaved.setDescEs(SpringApplicationContextHelper.getMessage("metric.perc.saved", new Locale("es", "ES")));
                percSaved.setIsPercentage(true);
                tmpSimilarityMetrics.put(903L, percSaved);

                // Metrica Tocchi
                AdvancedMetric touchesSaved = new AdvancedMetric();
                touchesSaved.setCode("touches");
                touchesSaved.setId(904L);
                touchesSaved.setDesc(SpringApplicationContextHelper.getMessage("metric.perc.touches", Locale.ITALIAN));
                touchesSaved.setDescEn(SpringApplicationContextHelper.getMessage("metric.perc.touches", Locale.ENGLISH));
                touchesSaved.setDescFr(SpringApplicationContextHelper.getMessage("metric.perc.touches", Locale.FRENCH));
                touchesSaved.setDescEs(SpringApplicationContextHelper.getMessage("metric.perc.touches", new Locale("es", "ES")));
                tmpSimilarityMetrics.put(904L, touchesSaved);

                similarityMetrics = tmpSimilarityMetrics;
            }
        }

        if (playerAgencies.isEmpty() || BooleanUtils.isTrue(force)) {
            List<PlayerAgency> playerAgencyList = mService.getPlayerAgencies();
            if (playerAgencyList != null && !playerAgencyList.isEmpty()) {
                Map<Long, PlayerAgency> tmpPlayerAgencies = new HashMap<>();
                for (PlayerAgency playerAgency : playerAgencyList) {
                    tmpPlayerAgencies.put(playerAgency.getId(), playerAgency);
                }

                playerAgencies = tmpPlayerAgencies;
            }
        }

        if (groups.isEmpty() || BooleanUtils.isTrue(force)) {
            List<Group> groupList = mService.getGroups();
            if (groupList != null && !groupList.isEmpty()) {
                Map<Integer, Group> tmpGroups = new HashMap<>();
                for (Group group : groupList) {
                    tmpGroups.put(group.getId(), group);
                }

                groups = tmpGroups;
            }
        }
//        System.out.println("(!) advancedMetrics loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

        if (BooleanUtils.isTrue(force)) {
            // notifico tutti
            for (Long userId : WebSocketEndpoint.getClients().keySet()) {
                if (userId != null) {
                    List<HttpSession> sessions = SessionListener.getUserSessions(userId);
                    Session webSocketSession = WebSocketEndpoint.getClients().get(userId);
                    if (sessions != null && !sessions.isEmpty() && webSocketSession != null) {
                        HttpSession session = sessions.get(0);
                        if (session.getAttribute(GlobalHelper.kBeanLanguage) != null) {
                            String sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
                            Locale locale = Locale.forLanguageTag(sessionLanguage);
                            if (locale != null) {
                                WebSocketEndpoint.send(webSocketSession, MessageType.SUCCESS, SpringApplicationContextHelper.getMessage("data.updated", locale));
                            }
                        }
                    }
                }
            }
        }

        System.out.println("(!) Cache loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

//        List<Long> playerIds = new ArrayList<>(players.keySet());
//        MongoHelper.removeOldRows(playerIds);
    }

    public static Map<Long, Season> getSeasons() {
        return seasons;
    }

    public static void setSeasons(Map<Long, Season> seasons) {
        BaseController.seasons = seasons;
    }

    public static Map<Long, Season> getGroupedSeasons() {
        return groupedSeasons;
    }

    public static void setGroupedSeasons(Map<Long, Season> groupedSeasons) {
        BaseController.groupedSeasons = groupedSeasons;
    }

    public static Map<Long, Team> getTeams() {
        return teams;
    }

    public static void setTeams(Map<Long, Team> teams) {
        BaseController.teams = teams;
    }

    public static Map<Long, Competition> getCompetitions() {
        return competitions;
    }

    public static void setCompetitions(Map<Long, Competition> competitions) {
        BaseController.competitions = competitions;
    }

    public static Map<Long, EventType> getEventTypes() {
        return eventTypes;
    }

    public static void setEventTypes(Map<Long, EventType> eventTypes) {
        BaseController.eventTypes = eventTypes;
    }

    public static Map<Long, EventType> getOppositeEventTypes() {
        return oppositeEventTypes;
    }

    public static void setOppositeEventTypes(Map<Long, EventType> oppositeEventTypes) {
        BaseController.oppositeEventTypes = oppositeEventTypes;
    }

    public static Map<Long, TagType> getTagTypes() {
        return tagTypes;
    }

    public static void setTagTypes(Map<Long, TagType> tagTypes) {
        BaseController.tagTypes = tagTypes;
    }

    public static Map<Long, TagType> getOppositeTagTypes() {
        return oppositeTagTypes;
    }

    public static void setOppositeTagTypes(Map<Long, TagType> oppositeTagTypes) {
        BaseController.oppositeTagTypes = oppositeTagTypes;
    }

    public static List<Long> getSufferedEventTypes() {
        return sufferedEventTypes;
    }

    public static void setSufferedEventTypes(List<Long> sufferedEventTypes) {
        BaseController.sufferedEventTypes = sufferedEventTypes;
    }

    public static Map<Long, Player> getPlayers() {
        return players;
    }

    public static void setPlayers(Map<Long, Player> players) {
        BaseController.players = players;
    }

    public static Map<Long, FixtureDetails> getFixtureDetails() {
        return fixtureDetails;
    }

    public static void setFixtureDetails(Map<Long, FixtureDetails> fixtureDetails) {
        BaseController.fixtureDetails = fixtureDetails;
    }

    public static Map<Long, Country> getCountries() {
        return countries;
    }

    public static void setCountries(Map<Long, Country> countries) {
        BaseController.countries = countries;
    }

    public static Map<Integer, Foot> getFoots() {
        return foots;
    }

    public static void setFoots(Map<Integer, Foot> foots) {
        BaseController.foots = foots;
    }

    public static Map<Integer, Position> getPositions() {
        return positions;
    }

    public static void setPositions(Map<Integer, Position> positions) {
        BaseController.positions = positions;
    }

    public static Map<Integer, Position> getPositionDetails() {
        return positionDetails;
    }

    public static void setPositionDetails(Map<Integer, Position> positionDetails) {
        BaseController.positionDetails = positionDetails;
    }

    public static Map<Long, Map<Long, Map<Long, TeamPlayer>>> getSeasonTeamPlayerPositions() {
        return seasonTeamPlayerPositions;
    }

    public static void setSeasonTeamPlayerPositions(Map<Long, Map<Long, Map<Long, TeamPlayer>>> seasonTeamPlayerPositions) {
        BaseController.seasonTeamPlayerPositions = seasonTeamPlayerPositions;
    }

    public static Map<Long, AdvancedMetric> getAdvancedMetrics() {
        return advancedMetrics;
    }

    public static void setAdvancedMetrics(Map<Long, AdvancedMetric> advancedMetrics) {
        BaseController.advancedMetrics = advancedMetrics;
    }

    /*

        END CACHE

     */
    public BaseController() {

    }

    protected boolean initModule(HttpSession session, ModelMap model, HttpServletRequest request, HttpServletResponse response) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        model.addAttribute("mUser", curUser);
        model.addAttribute("mLanguage", (String) session.getAttribute(GlobalHelper.kBeanLanguage));

        // model.addAttribute("mGlobalTeams", teams);
        boolean isLocal = GlobalHelper.isLocalEnvironment();
        if (isLocal) {
            model.addAttribute("mBaseUrl", request.getServerName() + ":" + request.getServerPort() + request.getContextPath());
        } else {
            model.addAttribute("mBaseUrl", request.getServerName() + ":8080" + request.getContextPath());
        }

        Locale locale = null;
        if (curUser != null) {
            if (StringUtils.isBlank(curUser.getTvLanguage())) {
                // carico settings
                try {
                    Settings userSettings = mService.getSettingsByUserId(curUser.getId());
                    if (userSettings == null) {
                        userSettings = new Settings();
                        userSettings.setUserId(curUser.getId());
                        userSettings.setApplication(1);
                        userSettings.setEventAdditionalStart(0L);
                        userSettings.setEventAdditionalEnd(0L);
                        userSettings.setTvLanguage(StringUtils.defaultIfEmpty(curUser.getLanguage(), "en"));

                        mService.addSettings(userSettings);
                    }

                    // viene impostato basandosi sulla colonna "language" di user
                    curUser.setTvLanguage(userSettings.getTvLanguage());
                } catch (Exception ex) {
                    GlobalHelper.reportError(ex);
                } finally {
                    if (StringUtils.isBlank(curUser.getTvLanguage())) {
                        // sicurezza
                        curUser.setTvLanguage("en");
                    }
                }
            }

            locale = getLocaleFromLanguange(curUser.getTvLanguage());

            if (curUser.getPreferredTeamId() != null) {
                if (teams.containsKey(curUser.getPreferredTeamId())) {
                    model.addAttribute("mPreferredTeam", teams.get(curUser.getPreferredTeamId()));
                }
            }
        } else {
            locale = new Locale(Locale.ENGLISH.getLanguage());
        }
        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);

        Map<String, String> globalMessages = new HashMap<>();
        globalMessages.put("error.unexpected", SpringApplicationContextHelper.getMessage("error.unexpected", locale));
        globalMessages.put("server.message", SpringApplicationContextHelper.getMessage("server.message", locale));
        globalMessages.put("action.confirmation", SpringApplicationContextHelper.getMessage("action.confirmation", locale));
        globalMessages.put("action.yes", SpringApplicationContextHelper.getMessage("action.yes", locale));
        globalMessages.put("action.no", SpringApplicationContextHelper.getMessage("action.no", locale));
        globalMessages.put("filters.reset.confirm", SpringApplicationContextHelper.getMessage("filters.reset.confirm", locale));
        globalMessages.put("personal.filter.name", SpringApplicationContextHelper.getMessage("personal.filter.name", locale));
        globalMessages.put("personal.filter.name.default", SpringApplicationContextHelper.getMessage("personal.filter.name.default", locale));
        globalMessages.put("personal.filter.save", SpringApplicationContextHelper.getMessage("personal.filter.save", locale));
        globalMessages.put("personal.filter.cancel", SpringApplicationContextHelper.getMessage("personal.filter.cancel", locale));
        globalMessages.put("personal.filter.save.confirm", SpringApplicationContextHelper.getMessage("personal.filter.save.confirm", locale));
        globalMessages.put("personal.filter.loaded", SpringApplicationContextHelper.getMessage("personal.filter.loaded", locale));
        globalMessages.put("personal.filter.update", SpringApplicationContextHelper.getMessage("personal.filter.update", locale));
        globalMessages.put("personal.filter.update.confirm", SpringApplicationContextHelper.getMessage("personal.filter.update.confirm", locale));
        globalMessages.put("personal.filter.delete", SpringApplicationContextHelper.getMessage("personal.filter.delete", locale));
        globalMessages.put("personal.filter.delete.question", SpringApplicationContextHelper.getMessage("personal.filter.delete.question", locale));
        globalMessages.put("personal.filter.delete.confirm", SpringApplicationContextHelper.getMessage("personal.filter.delete.confirm", locale));
        globalMessages.put("filters.angle.zone.lateral", SpringApplicationContextHelper.getMessage("filters.angle.zone.lateral", locale));
        globalMessages.put("filters.angle.zone.diagonal", SpringApplicationContextHelper.getMessage("filters.angle.zone.diagonal", locale));
        globalMessages.put("filters.angle.zone.vertical", SpringApplicationContextHelper.getMessage("filters.angle.zone.vertical", locale));
        globalMessages.put("filters.angle.zone.back", SpringApplicationContextHelper.getMessage("filters.angle.zone.back", locale));
        globalMessages.put("player.distribution.first", SpringApplicationContextHelper.getMessage("player.distribution.first", locale));
        globalMessages.put("player.distribution.second", SpringApplicationContextHelper.getMessage("player.distribution.second", locale));
        globalMessages.put("player.distribution.third", SpringApplicationContextHelper.getMessage("player.distribution.third", locale));
        globalMessages.put("menu.player.minutes.played", SpringApplicationContextHelper.getMessage("menu.player.minutes.played", locale));
        globalMessages.put("menu.player.value", SpringApplicationContextHelper.getMessage("menu.player.value", locale));
        globalMessages.put("player.distribution.best.three", SpringApplicationContextHelper.getMessage("player.distribution.best.three", locale));
        globalMessages.put("player.distribution.worst.three", SpringApplicationContextHelper.getMessage("player.distribution.worst.three", locale));
        globalMessages.put("messages.average", SpringApplicationContextHelper.getMessage("messages.average", locale));
        globalMessages.put("messages.generic.average", SpringApplicationContextHelper.getMessage("messages.generic.average", locale));
        globalMessages.put("filters.event.advanced.metrics", SpringApplicationContextHelper.getMessage("filters.event.advanced.metrics", locale));
        globalMessages.put("filters.event.tactical", SpringApplicationContextHelper.getMessage("filters.event.tactical", locale));
        globalMessages.put("positional.defensive.half", SpringApplicationContextHelper.getMessage("positional.defensive.half", locale));
        globalMessages.put("positional.offensive.half", SpringApplicationContextHelper.getMessage("positional.offensive.half", locale));
        globalMessages.put("positional.zone.one", SpringApplicationContextHelper.getMessage("positional.zone.one", locale));
        globalMessages.put("positional.zone.two", SpringApplicationContextHelper.getMessage("positional.zone.two", locale));
        globalMessages.put("positional.zone.three", SpringApplicationContextHelper.getMessage("positional.zone.three", locale));
        globalMessages.put("positional.channel.one", SpringApplicationContextHelper.getMessage("positional.channel.one", locale));
        globalMessages.put("positional.channel.two", SpringApplicationContextHelper.getMessage("positional.channel.two", locale));
        globalMessages.put("positional.channel.three", SpringApplicationContextHelper.getMessage("positional.channel.three", locale));
        globalMessages.put("positional.channel.four", SpringApplicationContextHelper.getMessage("positional.channel.four", locale));
        globalMessages.put("positional.channel.five", SpringApplicationContextHelper.getMessage("positional.channel.five", locale));
        globalMessages.put("positional.zone.one.channel.one", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.one", locale));
        globalMessages.put("positional.zone.one.channel.two", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.two", locale));
        globalMessages.put("positional.zone.one.channel.three", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.three", locale));
        globalMessages.put("positional.zone.one.channel.four", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.four", locale));
        globalMessages.put("positional.zone.one.channel.five", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.five", locale));
        globalMessages.put("positional.zone.two.channel.one", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.one", locale));
        globalMessages.put("positional.zone.two.channel.two", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.two", locale));
        globalMessages.put("positional.zone.two.channel.three", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.three", locale));
        globalMessages.put("positional.zone.two.channel.four", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.four", locale));
        globalMessages.put("positional.zone.two.channel.five", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.five", locale));
        globalMessages.put("positional.zone.three.channel.one", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.one", locale));
        globalMessages.put("positional.zone.three.channel.two", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.two", locale));
        globalMessages.put("positional.zone.three.channel.three", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.three", locale));
        globalMessages.put("positional.zone.three.channel.four", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.four", locale));
        globalMessages.put("positional.zone.three.channel.five", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.five", locale));
        globalMessages.put("positional.penalty.area", SpringApplicationContextHelper.getMessage("positional.penalty.area", locale));
        globalMessages.put("positional.small.area", SpringApplicationContextHelper.getMessage("positional.small.area", locale));
        globalMessages.put("positional.opponent.penalty.area", SpringApplicationContextHelper.getMessage("positional.opponent.penalty.area", locale));
        globalMessages.put("positional.opponent.small.area", SpringApplicationContextHelper.getMessage("positional.opponent.small.area", locale));
        globalMessages.put("positional.defensive.half.abb", SpringApplicationContextHelper.getMessage("positional.defensive.half.abb", locale));
        globalMessages.put("positional.offensive.half.abb", SpringApplicationContextHelper.getMessage("positional.offensive.half.abb", locale));
        globalMessages.put("positional.zone.one.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.abb", locale));
        globalMessages.put("positional.zone.two.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.abb", locale));
        globalMessages.put("positional.zone.three.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.abb", locale));
        globalMessages.put("positional.channel.one.abb", SpringApplicationContextHelper.getMessage("positional.channel.one.abb", locale));
        globalMessages.put("positional.channel.two.abb", SpringApplicationContextHelper.getMessage("positional.channel.two.abb", locale));
        globalMessages.put("positional.channel.three.abb", SpringApplicationContextHelper.getMessage("positional.channel.three.abb", locale));
        globalMessages.put("positional.channel.four.abb", SpringApplicationContextHelper.getMessage("positional.channel.four.abb", locale));
        globalMessages.put("positional.channel.five.abb", SpringApplicationContextHelper.getMessage("positional.channel.five.abb", locale));
        globalMessages.put("positional.zone.one.channel.one.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.one.abb", locale));
        globalMessages.put("positional.zone.one.channel.two.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.two.abb", locale));
        globalMessages.put("positional.zone.one.channel.three.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.three.abb", locale));
        globalMessages.put("positional.zone.one.channel.four.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.four.abb", locale));
        globalMessages.put("positional.zone.one.channel.five.abb", SpringApplicationContextHelper.getMessage("positional.zone.one.channel.five.abb", locale));
        globalMessages.put("positional.zone.two.channel.one.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.one.abb", locale));
        globalMessages.put("positional.zone.two.channel.two.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.two.abb", locale));
        globalMessages.put("positional.zone.two.channel.three.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.three.abb", locale));
        globalMessages.put("positional.zone.two.channel.four.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.four.abb", locale));
        globalMessages.put("positional.zone.two.channel.five.abb", SpringApplicationContextHelper.getMessage("positional.zone.two.channel.five.abb", locale));
        globalMessages.put("positional.zone.three.channel.one.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.one.abb", locale));
        globalMessages.put("positional.zone.three.channel.two.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.two.abb", locale));
        globalMessages.put("positional.zone.three.channel.three.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.three.abb", locale));
        globalMessages.put("positional.zone.three.channel.four.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.four.abb", locale));
        globalMessages.put("positional.zone.three.channel.five.abb", SpringApplicationContextHelper.getMessage("positional.zone.three.channel.five.abb", locale));
        globalMessages.put("positional.penalty.area.abb", SpringApplicationContextHelper.getMessage("positional.penalty.area.abb", locale));
        globalMessages.put("positional.small.area.abb", SpringApplicationContextHelper.getMessage("positional.small.area.abb", locale));
        globalMessages.put("positional.opponent.penalty.area.abb", SpringApplicationContextHelper.getMessage("positional.opponent.penalty.area.abb", locale));
        globalMessages.put("positional.opponent.small.area.abb", SpringApplicationContextHelper.getMessage("positional.opponent.small.area.abb", locale));
        globalMessages.put("event.filter.max.filters.reached", SpringApplicationContextHelper.getMessage("event.filter.max.filters.reached", locale));
        globalMessages.put("event.filter.modal.parameter.added", SpringApplicationContextHelper.getMessage("event.filter.modal.parameter.added", locale));
        globalMessages.put("event.filter.modal.positional.half", SpringApplicationContextHelper.getMessage("event.filter.modal.positional.half", locale));
        globalMessages.put("event.filter.modal.positional.zone", SpringApplicationContextHelper.getMessage("event.filter.modal.positional.zone", locale));
        globalMessages.put("event.filter.modal.positional.channel", SpringApplicationContextHelper.getMessage("event.filter.modal.positional.channel", locale));
        globalMessages.put("event.filter.modal.positional.zone.channel", SpringApplicationContextHelper.getMessage("event.filter.modal.positional.zone.channel", locale));
        globalMessages.put("event.filter.modal.positional.area", SpringApplicationContextHelper.getMessage("event.filter.modal.positional.area", locale));
        globalMessages.put("messages.warning.missed.parameters", SpringApplicationContextHelper.getMessage("messages.warning.missed.parameters", locale));
        globalMessages.put("messages.playtime.filter", SpringApplicationContextHelper.getMessage("messages.playtime.filter", locale));
        globalMessages.put("personal.filter.too.long", SpringApplicationContextHelper.getMessage("personal.filter.too.long", locale));
        globalMessages.put("messages.playtime.filter.detailed", SpringApplicationContextHelper.getMessage("messages.playtime.filter.detailed", locale));
        globalMessages.put("pdf.screenshot.already.saving.warning", SpringApplicationContextHelper.getMessage("pdf.screenshot.already.saving.warning", locale));
        globalMessages.put("pdf.screenshot.library.not.loaded.error", SpringApplicationContextHelper.getMessage("pdf.screenshot.library.not.loaded.error", locale));
        globalMessages.put("pdf.screenshot.saving.wait", SpringApplicationContextHelper.getMessage("pdf.screenshot.saving.wait", locale));
        globalMessages.put("pdf.screenshot.added.to.pdf.success", SpringApplicationContextHelper.getMessage("pdf.screenshot.added.to.pdf.success", locale));
        globalMessages.put("pdf.screenshot.no.pages.to.download.warning", SpringApplicationContextHelper.getMessage("pdf.screenshot.no.pages.to.download.warning", locale));
        globalMessages.put("pdf.screenshot.generating.pdf.wait", SpringApplicationContextHelper.getMessage("pdf.screenshot.generating.pdf.wait", locale));
        globalMessages.put("pdf.screenshot.pdf.downloaded.success", SpringApplicationContextHelper.getMessage("pdf.screenshot.pdf.downloaded.success", locale));
        globalMessages.put("pdf.screenshot.delete.pdf.data.confirm", SpringApplicationContextHelper.getMessage("pdf.screenshot.delete.pdf.data.confirm", locale));
        globalMessages.put("pdf.screenshot.generating.pdf.error", SpringApplicationContextHelper.getMessage("pdf.screenshot.generating.pdf.error", locale));
        globalMessages.put("pdf.screenshot.pdf.data.deleted.success", SpringApplicationContextHelper.getMessage("pdf.screenshot.pdf.data.deleted.success", locale));
        model.addAttribute("mGlobalMessages", globalMessages);

        return (curUser != null);
    }

    public static Locale getLocaleFromLanguange(String languange) {
        Locale locale = null;
        if (StringUtils.equals(languange, "es")) {
            locale = new Locale("es", "ES");
        } else {
            locale = new Locale(StringUtils.defaultIfEmpty(languange, Locale.ENGLISH.getLanguage()));
        }

        return locale;
    }

    protected String pageRedirect(String page) {
        return PAGE_REDIRECT + "/" + page.replace(".jsp", ".htm");
    }

}
