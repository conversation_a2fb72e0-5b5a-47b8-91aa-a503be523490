package sics.service;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import sics.domain.*;
import sics.helper.GlobalHelper;

public class UserService extends BaseService {

    public User getUser(Long id) {
        try {
            return dao.getUser(id);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getUsers() {
        try {
            return dao.getUsers();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public User getUserByMail(String mail) {
        try {
            return dao.getUserByMail(mail);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getUsers(List<Long> ids) {
        try {
            return dao.getUsers(ids);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getUsersWithDetails(List<Long> ids) {
        try {
            return dao.getUsersWithDetails(ids);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getUsersByGroupset(Long groupsetId) {
        try {
            return dao.getUsersByGroupset(groupsetId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<UserPermission> getUserPermissions(Long userId) {
        try {
            return dao.getUserPermissions(userId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Competition> getUserCompetitions(Long groupsetId) {
        try {
            return dao.getUserCompetitions(groupsetId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public void saveUser(User item) {
        try {
            dao.saveUser(item);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public void saveLogData(LogData item) {
        try {
            dao.saveLogData(item);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public List<Team> getTeams() {
        try {
            return dao.getTeams();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Team> getTeams(List<Long> teamIds) {
        try {
            return dao.getTeams(teamIds);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Season> getSeasons() {
        try {
            return dao.getSeasons();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Competition> getCompetitions() {
        try {
            return dao.getCompetitions();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Competition> getCompetitions(List<Long> competitionIds) {
        try {
            return dao.getCompetitions(competitionIds);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Competition getCompetition(Long competitionId) {
        try {
            return dao.getCompetition(competitionId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Competition> getInternationalCompetitions() {
        try {
            return dao.getInternationalCompetitions();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Settings getSettingsByUserId(Long userId) {
        try {
            return dao.getSettingsByUserId(userId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Long> getTeamByGroupsetId(List<Competition> allowedCompetitions) {
        try {
            return dao.getTeamByGroupsetId(allowedCompetitions);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Long> getPlayersByGroupsetId(List<Competition> allowedCompetitions) {
        try {
            return dao.getPlayersByGroupsetId(allowedCompetitions);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<EventType> getEventTypes() {
        try {
            return dao.getEventTypes();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<EventType> getOppositeEventTypes() {
        try {
            return dao.getOppositeEventTypes();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<TagType> getTagTypes() {
        try {
            return dao.getTagTypes();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Player> getPlayers() {
        try {
            return dao.getPlayers();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Player getPlayer(Long playerId, Boolean isMatchStudioQuery) {
        try {
            return dao.getPlayer(playerId, isMatchStudioQuery);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<FixtureDetails> getFixtureDetails() {
        try {
            return dao.getFixtureDetails();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Country> getCountries() {
        try {
            return dao.getCountries();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Foot> getFoots() {
        try {
            return dao.getFoots();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Position> getPositions() {
        try {
            return dao.getPositions();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Position> getPositionDetails() {
        try {
            return dao.getPositionDetails();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<TeamPlayer> getTeamPlayers() {
        try {
            return dao.getTeamPlayers();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<AdvancedMetric> getAdvancedMetrics() {
        try {
            return dao.getAdvancedMetrics();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Fixture getFixtureById(Long fixtureId) {
        try {
            return dao.getFixtureById(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Fixture> getFixtureByIds(List<Long> fixtureIds) {
        try {
            return dao.getFixtureByIds(fixtureIds);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Event> getEventsByParams(Map<String, Object> params) {
        try {
            return dao.getEventsByParams(params);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<FixturePlayer> getFixturePlayersByParams(Map<String, Object> params) {
        try {
            return dao.getFixturePlayersByParams(params);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public void addSettings(Settings settings) {
        try {
            dao.addSettings(settings);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public void updateSettings(Settings settings) {
        try {
            dao.updateSettings(settings);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public List<Filter> getFiltersByPage(Long userId, Long groupsetId, String page) {
        try {
            return dao.getFiltersByPage(userId, groupsetId, page);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Long saveFilter(Filter filter) {
        try {
            return dao.saveFilter(filter);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public void updateFilter(Filter filter) {
        try {
            dao.updateFilter(filter);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public void deleteFilter(Long filterId) {
        try {
            dao.deleteFilter(filterId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public void saveFilterDetails(Filter filter) {
        try {
            dao.saveFilterDetails(filter);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public void deleteFilterDetails(Long filterId) {
        try {
            dao.deleteFilterDetails(filterId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public List<TeamCareerItem> getTeamCareer(Long teamId) {
        try {
            return dao.getTeamCareer(teamId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Fixture> getTeamLastFixtures(Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getTeamLastFixtures(teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public TeamData getTeamData(Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getTeamData(teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public TeamPlayerData getTeamPlayerData(Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getTeamPlayerData(teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerData(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerData(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerWithMostPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerWithMostPasses(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerWithMostReceivedPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerWithMostReceivedPasses(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<PlayerData> getPlayerModules(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerModules(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerPlaytimeStats(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerPlaytimeStats(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerStarterStats(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerStarterStats(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<PlayerCareerItem> getPlayerCareer(Long playerId) {
        try {
            return dao.getPlayerCareer(playerId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Fixture> getPlayerLastFixtures(Long playerId, Long teamId, Long competitionId, Long seasonId) {
        try {
            return dao.getPlayerLastFixtures(playerId, teamId, competitionId, seasonId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public String getFixtureFileproject(Long fixtureId) {
        try {
            return dao.getFixtureFileproject(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Groupset getGroupset(Long groupsetId) {
        try {
            return dao.getGroupset(groupsetId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public Long getUserLastWeekMatchDownloadAmount(Long userId) {
        try {
            return dao.getUserLastWeekMatchDownloadAmount(userId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public void resetUserPassword(Long userId) {
        try {
            dao.resetUserPassword(userId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public TeamPlayerLast getTeamLastData(Long teamId) {
        try {
            return dao.getTeamLastData(teamId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public TeamPlayerLast getPlayerLastData(Long playerId, List<Competition> allowedCompetitions) {
        try {
            return dao.getPlayerLastData(playerId, allowedCompetitions);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }
    
    public List<AdvancedMetric> getSimilarityMetrics() {
        try {
            return dao.getSimilarityMetrics();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<PlayerAgency> getPlayerAgencies() {
        try {
            return dao.getPlayerAgencies();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Group> getGroups() {
        try {
            return dao.getGroups();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public PlayerData getPlayerLastTeam(Long playerId) {
        try {
            return dao.getPlayerLastTeam(playerId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<PlayerData> getPlayersLastTeam(List<Long> playerIds) {
        try {
            return dao.getPlayersLastTeam(playerIds);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<PlayerData> getPlayersCompetitions(List<Long> playerIds) {
        try {
            return dao.getPlayersCompetitions(playerIds);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<Long> getTeamCompetitions(Long teamId, Long seasonId, List<Competition> allowedCompetitions) {
        try {
            return dao.getTeamCompetitions(teamId, seasonId, allowedCompetitions);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public FixtureDetails getFixtureDetails(Long fixtureId, Boolean isMatchStudioQuery) {
        try {
            return dao.getFixtureDetails(fixtureId, isMatchStudioQuery);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getVtigerAgents() {
        try {
            return dao.getVtigerAgents();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<User> getVtigerAgentClients(Long agentId) {
        try {
            return dao.getVtigerAgentClients(agentId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<AdvancedMetricValue> getMatchStudioTeamAdvancedMetrics(Long fixtureId) {
        try {
            return dao.getMatchStudioTeamAdvancedMetrics(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<AdvancedMetricValue> getMatchStudioPlayerAdvancedMetrics(Long fixtureId) {
        try {
            return dao.getMatchStudioPlayerAdvancedMetrics(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<IndexEvent> getFixtureIndexEvent(Long fixtureId) {
        try {
            return dao.getFixtureIndexEvent(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<IndexEventType> getIndexEventType() {
        try {
            return dao.getIndexEventType();
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public List<IndexTrend> getFixtureIndexTrend(Long fixtureId) {
        try {
            return dao.getFixtureIndexTrend(fixtureId);
        } catch (SQLException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }
}
